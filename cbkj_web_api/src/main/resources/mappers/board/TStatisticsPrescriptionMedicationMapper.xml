<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.board.TStatisticsPrescriptionMedicationMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
        <result column="number" jdbcType="INTEGER" property="number" />
    </resultMap>


    <sql id="Base_Column_List">
    app_id,app_name,ins_code,ins_name,dept_id,create_date,insert_date,number
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication">
        delete from t_statistics_prescription_medication where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_prescription_medication where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication">
        insert into t_statistics_prescription_medication (<include refid="Base_Column_List" />) values
        (#{appId},#{appName},#{insCode},#{insName},#{deptId},#{createDate},#{insertDate},#{number})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_prescription_medication (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.createDate},#{item.insertDate},#{item.number})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication">
        update t_statistics_prescription_medication
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                insert_date = #{ insertDate },
             </if>
             <if test="number != null">
                 number = #{number},
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_prescription_medication where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,create_date,insert_date,number
        from t_statistics_prescription_medication
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getCountByObj" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication" resultType="java.lang.Long">
        select  count(*) from t_statistics_prescription_medication
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id=#{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code=#{insCode}
            </if>

            <if test=" createDate != null">
                and create_date=#{createDate}
            </if>
        </where>
    </select>
    <select id="getOneByObject"  resultMap="BaseResultMap"  parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication">
        select * from t_statistics_prescription_medication
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id=#{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code=#{insCode}
            </if>
            <if test=" createDate != null">
                and create_date=#{createDate}
            </if>
        </where>
limit 1
    </select>
    <select id="sumStatistics" resultType="java.lang.Integer">
        select
            SUM(number) as number
        from t_statistics_prescription_medication
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
    </select>

</mapper>