<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionMapper">
    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription">
        <id column="PERS_PRE_ID" jdbcType="VARCHAR" property="persPreId"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName"/>
        <result column="PRE_OWNER" jdbcType="VARCHAR" property="preOwner"/>
        <result column="PRE_OWNER_NAME" jdbcType="VARCHAR" property="preOwnerName"/>
        <result column="PRE_NAME" jdbcType="VARCHAR" property="preName"/>
        <result column="EFFICACY" jdbcType="VARCHAR" property="efficacy"/>
        <result column="ANNOTATION" jdbcType="VARCHAR" property="annotation"/>
        <result column="PRE_TYPE" jdbcType="VARCHAR" property="preType"/>
        <result column="IS_SHARE" jdbcType="VARCHAR" property="isShare"/>
        <result column="PRE_PY" jdbcType="VARCHAR" property="prePy"/>
        <result column="PRE_WB" jdbcType="VARCHAR" property="preWb"/>
        <result column="IS_MODIFY" jdbcType="VARCHAR" property="isModify"/>
        <result column="DIS_ID" jdbcType="VARCHAR" property="disId"/>
        <result column="DIS_NAME" jdbcType="VARCHAR" property="disName"/>
        <result column="SYM_ID" jdbcType="VARCHAR" property="symId"/>
        <result column="SYM_NAME" jdbcType="VARCHAR" property="symName"/>
        <result column="PRE_EXPERT" jdbcType="VARCHAR" property="preExpert"/>
        <result column="PRE_EXPERT_TITLE" jdbcType="VARCHAR" property="preExpertTitle"/>
        <result column="INSERT_DATE" jdbcType="TIMESTAMP" property="insertDate"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate"/>
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel"/>
        <result column="PRE_ORIGIN" jdbcType="VARCHAR" property="preOrigin"/>
        <result column="PRE_NUM" jdbcType="SMALLINT" property="preNum"/>
        <result column="PRE_DESCRIPTION_ID" jdbcType="VARCHAR" property="preDescriptionId"/>
        <result column="PRE_DESCRIPTION" jdbcType="VARCHAR" property="preDescription"/>
        <result column="PRE_FREQUENCY_ID" jdbcType="VARCHAR" property="preFrequencyId"/>
        <result column="PRE_FREQUENCY" jdbcType="VARCHAR" property="preFrequency"/>
        <result column="PRE_SMOKE_TYPE_ID" jdbcType="VARCHAR" property="preSmokeTypeId"/>
        <result column="PRE_SMOKE_TYPE" jdbcType="VARCHAR" property="preSmokeType"/>
        <result column="PRE_MAT_TYPE" jdbcType="VARCHAR" property="preMatType"/>
        <result column="STORE_ID" jdbcType="VARCHAR" property="storeId"/>
        <result column="pre_mz_zy" jdbcType="VARCHAR" property="preMzZy"/>
        <result column="PRE_ORDER" jdbcType="INTEGER" property="preOrder"/>
        <result column="IS_PREPARATION" jdbcType="INTEGER" property="isPreparation"/>
        <result column="CAN_APPEND" jdbcType="INTEGER" property="canAppend"/>
        <result column="preItem" jdbcType="VARCHAR" property="preItem"/>
        <result column="IS_USE" jdbcType="VARCHAR" property="isUse"/>
        <result column="IS_SCIENTIFIC_PREPARATION" jdbcType="INTEGER" property="isScientificPreparation"/>
        <result column="INFO_SCIENTIFIC_PREPARATION" jdbcType="VARCHAR" property="infoScientificPreparation"/>
        <collection property="tPersonalPrescriptionDisMappingsList" ofType="com.jiuzhekan.cbkj.beans.business.prescription.TPersonalPrescriptionDisMapping">
            <id column="m_id" jdbcType="INTEGER"  property="id" />
            <result column="M_PERS_PRE_ID" jdbcType="VARCHAR" property="persPreId" />
            <result column="m_dis_id" jdbcType="VARCHAR" property="disId" />
            <result column="m_dis_name" jdbcType="VARCHAR" property="disName" />
            <result column="m_sym_id" jdbcType="VARCHAR" property="symId" />
            <result column="m_sym_name" jdbcType="VARCHAR" property="symName" />
            <result column="m_the_code" jdbcType="VARCHAR" property="theCode" />
            <result column="m_the_names" jdbcType="VARCHAR" property="theNames" />
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
        PERS_PRE_ID,APP_ID,INS_CODE,DEPT_ID,PRE_OWNER,PRE_OWNER_NAME,PRE_NAME,EFFICACY,ANNOTATION,PRE_TYPE,IS_SHARE,PRE_PY,PRE_WB
    ,INSERT_DATE,UPDATE_DATE,DEL_DATE,IS_DEL,IS_MODIFY,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,PRE_EXPERT,PRE_EXPERT_TITLE
    ,PRE_ORIGIN,PRE_NUM,PRE_DESCRIPTION_ID,PRE_DESCRIPTION,PRE_FREQUENCY_ID,PRE_FREQUENCY,PRE_SMOKE_TYPE_ID,PRE_SMOKE_TYPE
    ,PRE_MAT_TYPE,STORE_ID,pre_mz_zy,PRE_ORDER,IS_PREPARATION,CAN_APPEND,IS_USE,IS_SCIENTIFIC_PREPARATION,INFO_SCIENTIFIC_PREPARATION
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPersonalPrescription">
        delete
        from t_personal_prescription
        where PERS_PRE_ID = #{ persPreId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        update t_personal_prescription set IS_DEL = '1' ,DEL_DATE = NOW() where PERS_PRE_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TPersonalPrescription">
        insert into t_personal_prescription
        ( PERS_PRE_ID, APP_ID, INS_CODE, DEPT_ID, PRE_OWNER, PRE_OWNER_NAME, PRE_NAME, EFFICACY, ANNOTATION, PRE_TYPE
        , IS_SHARE, PRE_PY, PRE_WB
        , INSERT_DATE, IS_DEL, IS_MODIFY, DIS_ID, DIS_NAME, SYM_ID, SYM_NAME, PRE_EXPERT, PRE_EXPERT_TITLE
        , PRE_ORIGIN, PRE_NUM, PRE_DESCRIPTION_ID, PRE_DESCRIPTION, PRE_FREQUENCY_ID, PRE_FREQUENCY, PRE_SMOKE_TYPE_ID
        , PRE_SMOKE_TYPE
        , PRE_MAT_TYPE, STORE_ID, pre_mz_zy, PRE_ORDER, IS_PREPARATION, CAN_APPEND, IS_USE, IS_SCIENTIFIC_PREPARATION,
         INFO_SCIENTIFIC_PREPARATION)
        values (#{persPreId}, #{appId}, #{insCode}, #{deptId}, #{preOwner}, #{preOwnerName}, #{preName}, #{efficacy},
                #{annotation}, #{preType}, #{isShare}, #{prePy}, #{preWb}, now(), #{isDel}, #{isModify}, #{disId},
                #{disName}, #{symId}, #{symName}, #{preExpert}, #{preExpertTitle}, #{preOrigin}, #{preNum},
                #{preDescriptionId}, #{preDescription}, #{preFrequencyId}, #{preFrequency}, #{preSmokeTypeId},
                #{preSmokeType}, #{preMatType}, #{storeId}, #{preMzZy}, #{preOrder}, #{isPreparation}, #{canAppend},
                #{isUse},#{isScientificPreparation},#{infoScientificPreparation})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_personal_prescription
        (PERS_PRE_ID,APP_ID,INS_CODE,DEPT_ID,PRE_OWNER,PRE_OWNER_NAME,PRE_NAME,EFFICACY,ANNOTATION,PRE_TYPE,IS_SHARE,PRE_PY,PRE_WB
        ,INSERT_DATE,IS_DEL,IS_MODIFY,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,PRE_EXPERT,PRE_EXPERT_TITLE
        ,PRE_ORIGIN,PRE_NUM,PRE_DESCRIPTION_ID,PRE_DESCRIPTION,PRE_FREQUENCY_ID,PRE_FREQUENCY,PRE_SMOKE_TYPE_ID,PRE_SMOKE_TYPE
        ,PRE_MAT_TYPE,STORE_ID,pre_mz_zy,PRE_ORDER,IS_PREPARATION,CAN_APPEND,IS_USE,IS_SCIENTIFIC_PREPARATION,INFO_SCIENTIFIC_PREPARATION ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.persPreId},#{item.appId},#{item.insCode},#{item.deptId},#{item.preOwner},#{item.preOwnerName},#{item.preName}
            ,#{item.efficacy},#{item.annotation},#{item.preType}
            ,#{item.isShare},#{item.prePy},#{item.preWb},now(),
             #{item.isDel},#{item.isModify},#{item.disId},#{item.disName},#{item.symId},#{item.symName}
            ,#{item.preExpert},#{item.preExpertTitle},#{item.preOrigin},#{item.preNum},#{item.preDescriptionId},#{item.preDescription},
             #{item.preFrequencyId},#{item.preFrequency},#{item.preSmokeTypeId},#{item.preSmokeType},#{item.preMatType},#{item.storeId},
             #{item.preMzZy},#{item.preOrder},#{item.isPreparation},#{item.canAppend},#{item.isUse},
            #{item.isScientificPreparation},#{item.infoScientificPreparation})


        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPersonalPrescription">
        update t_personal_prescription
        <set>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="deptId != null">
                DEPT_ID = #{ deptId },
            </if>
            <if test="preOwner != null">
                PRE_OWNER = #{ preOwner },
            </if>
            <if test="preOwnerName != null">
                PRE_OWNER_NAME = #{ preOwnerName },
            </if>
            <if test="preName != null">
                PRE_NAME = #{ preName },
            </if>
            <if test="efficacy != null">
                EFFICACY = #{ efficacy },
            </if>
            <if test="annotation != null">
                ANNOTATION = #{ annotation },
            </if>
            <if test="preType != null">
                PRE_TYPE = #{ preType },
            </if>
            <if test="isShare != null">
                IS_SHARE = #{ isShare },
            </if>
            <if test="prePy != null">
                PRE_PY = #{ prePy },
            </if>
            <if test="preWb != null">
                PRE_WB = #{ preWb },
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
            <if test="isModify != null">
                IS_MODIFY = #{ isModify },
            </if>
            <if test="isUse != null">
                IS_USE = #{isUse},
            </if>
            DIS_ID = #{ disId },
            DIS_NAME = #{ disName },
            SYM_ID = #{ symId },
            SYM_NAME = #{ symName },
            PRE_EXPERT = #{ preExpert },
            PRE_EXPERT_TITLE = #{ preExpertTitle },
            <if test="preOrigin != null">
                PRE_ORIGIN = #{ preOrigin },
            </if>
            <if test="preMzZy != null">
                pre_mz_zy = #{ preMzZy },
            </if>
            <if test="preOrder != null">
                PRE_ORDER = #{ preOrder },
            </if>
            <if test="isPreparation != null">
                IS_PREPARATION = #{ isPreparation },
            </if>
            <if test="canAppend != null">
                CAN_APPEND = #{ canAppend },
            </if>
            <if test="isScientificPreparation != null">
                IS_SCIENTIFIC_PREPARATION = #{isScientificPreparation},
            </if>
            <if test="infoScientificPreparation != null and infoScientificPreparation != ''">
                INFO_SCIENTIFIC_PREPARATION = #{infoScientificPreparation},
            </if>
            PRE_NUM = #{ preNum },
            PRE_DESCRIPTION_ID = #{ preDescriptionId },
            PRE_DESCRIPTION = #{ preDescription },
            PRE_FREQUENCY_ID = #{ preFrequencyId },
            PRE_FREQUENCY = #{ preFrequency },
            PRE_SMOKE_TYPE_ID = #{ preSmokeTypeId },
            PRE_SMOKE_TYPE = #{ preSmokeType },
            PRE_MAT_TYPE = #{ preMatType },
            STORE_ID = #{ storeId },
        </set>
        where PERS_PRE_ID = #{ persPreId }
    </update>

    <update id="updateOrder" parameterType="TPersonalPrescription">
        update t_personal_prescription
        set PRE_ORDER = #{ preOrder }
        where PERS_PRE_ID = #{ persPreId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_personal_prescription where PERS_PRE_ID = #{id}
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPersonalPrescription" resultMap="BaseResultMap">
        SELECT
        PERS_PRE_ID,APP_ID,INS_CODE,DEPT_ID,PRE_OWNER,PRE_OWNER_NAME,PRE_NAME,EFFICACY,ANNOTATION,PRE_TYPE,IS_SHARE,PRE_PY,PRE_WB,INSERT_DATE
        ,UPDATE_DATE,DEL_DATE,IS_DEL,IS_MODIFY,DIS_ID,DIS_NAME,SYM_ID,SYM_NAME,PRE_EXPERT,PRE_EXPERT_TITLE
        ,PRE_ORIGIN,PRE_NUM,PRE_DESCRIPTION_ID,PRE_DESCRIPTION,PRE_FREQUENCY_ID,PRE_FREQUENCY,PRE_SMOKE_TYPE_ID,PRE_SMOKE_TYPE,CAN_APPEND,
        IS_SCIENTIFIC_PREPARATION,INFO_SCIENTIFIC_PREPARATION
        from t_personal_prescription
        where 1=1 and IS_DEL = '0'
        <!--条件-->
        <!--<if test="name != null and name!='' ">-->
        <!--and name like CONCAT('%',trim(#{name}),'%')-->
        <!--</if>-->
    </select>

    <!-- ****************************   以下不是自动生成的   ****************************** -->


    <select id="getPageListByPer" parameterType="TPersonalPrescription" resultMap="BaseResultMap">
        SELECT t.*,
        (SELECT GROUP_CONCAT(i.YPMC_HIS SEPARATOR ',') FROM t_personal_prescription_item i WHERE i.PERS_PRE_ID =
        t.PERS_PRE_ID ) preItem
        FROM (
        SELECT t.*
        FROM t_personal_prescription AS t
        WHERE 1=0
        <if test="shareType != null and shareType.contains('0'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
<!--            <if test="(deptName != null and deptName != '') and ( insName == null or insName == '' )">-->
<!--                ,sys_department as dept-->
<!--                WHERE-->
<!--                (t.IS_DEL = '0' AND t.IS_SHARE = '0' AND t.INS_CODE = #{insCode}-->
<!--                and t.DEPT_ID=dept.DEP_ID-->
<!--                and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                <include refid="personalPreWhere"/>-->
<!--                )-->
<!--                or (t.IS_DEL = '0' AND t.IS_SHARE = '0' AND t.INS_CODE = #{insCode}-->
<!--                and-->
<!--                t.DEPT_ID=dept.DEP_ORIGIN_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                <include refid="personalPreWhere"/>-->
<!--                )-->
<!--            </if>-->

<!--            <if test="(deptName != null and deptName != '') and (insName != null and insName != '') ">-->
<!--                ,sys_department as dept,sys_institution as ins-->
<!--                WHERE-->

<!--                ( t.DEPT_ID=dept.DEP_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                and t.IS_DEL = '0' AND t.IS_SHARE = '0' AND t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID and ins.INS_NAME LIKE-->
<!--                CONCAT('%',TRIM(#{insName}),'%')-->
<!--                ) or-->
<!--                ( t.DEPT_ID=dept.DEP_ORIGIN_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                and t.IS_DEL = '0' AND t.IS_SHARE = '0' AND t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID and ins.INS_NAME LIKE-->
<!--                CONCAT('%',TRIM(#{insName}),'%')-->
<!--                )-->
<!--            </if>-->

<!--            <if test="(insName != null and insName != '') and (deptName == null or deptName == '')">-->
<!--                ,sys_department as dept,sys_institution as ins-->
<!--                WHERE-->
<!--                ( t.DEPT_ID=dept.DEP_ID-->
<!--                and dept.APP_ID=t.DEPT_ID and dept.INS_CODE = t.INS_CODE-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.APP_ID=dept.APP_ID and ins.INS_CODE = dept.INS_CODE-->
<!--                and ins.INS_NAME LIKE CONCAT('%',TRIM(#{insName}),'%') and t.IS_DEL = '0' AND t.IS_SHARE = '0' AND-->
<!--                t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                ) or(-->
<!--                t.DEPT_ID=dept.DEP_ORIGIN_ID and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_NAME LIKE CON-CAT('%',TRIM(#{insName}),'%') and t.IS_DEL = '0' AND t.IS_SHARE = '0' AND-->
<!--                t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
<!--            <if test="(insName == null or insName == '') and (deptName == null or deptName == '')">-->
                WHERE
                t.IS_DEL = '0' AND t.IS_SHARE = '0' AND t.INS_CODE = #{insCode}
                <include refid="personalPreWhere"/>
                <if test="preOwnerName != null and preOwnerName != ''">
                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')
                </if>
<!--            </if>-->
        </if>
        <if test="shareType != null and shareType.contains('1'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
<!--            <if test="(deptName != null and deptName != '') and ( insName == null or insName == '' )">-->
<!--                ,sys_department as dept-->
<!--                WHERE-->
<!--                (t.IS_DEL = '0' AND t.IS_SHARE = '1' AND t.INS_CODE = #{insCode}-->
<!--                and t.DEPT_ID=dept.DEP_ID-->
<!--                and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                <include refid="personalPreWhere"/>-->
<!--                )-->
<!--                or (t.IS_DEL = '0' AND t.IS_SHARE = '1' AND t.INS_CODE = #{insCode}-->
<!--                and-->
<!--                t.DEPT_ID=dept.DEP_ORIGIN_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                <include refid="personalPreWhere"/>-->
<!--                )-->
<!--            </if>-->

<!--            <if test="(deptName != null and deptName != '') and (insName != null and insName != '') ">-->
<!--                ,sys_department as dept,sys_institution as ins-->
<!--                WHERE-->

<!--                ( t.DEPT_ID=dept.DEP_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                and t.IS_DEL = '0' AND t.IS_SHARE = '1' AND t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID and ins.INS_NAME LIKE-->
<!--                CONCAT('%',TRIM(#{insName}),'%')-->
<!--                ) or-->
<!--                ( t.DEPT_ID=dept.DEP_ORIGIN_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                and t.IS_DEL = '0' AND t.IS_SHARE = '1' AND t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID and ins.INS_NAME LIKE-->
<!--                CONCAT('%',TRIM(#{insName}),'%')-->
<!--                )-->
<!--            </if>-->

<!--            <if test="(insName != null and insName != '') and (deptName == null or deptName == '')">-->
<!--                ,sys_department as dept,sys_institution as ins-->
<!--                WHERE-->
<!--                ( t.DEPT_ID=dept.DEP_ID-->
<!--                and dept.APP_ID=t.DEPT_ID and dept.INS_CODE = t.INS_CODE-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.APP_ID=dept.APP_ID and ins.INS_CODE = dept.INS_CODE-->
<!--                and ins.INS_NAME LIKE CONCAT('%',TRIM(#{insName}),'%') and t.IS_DEL = '0' AND t.IS_SHARE = '1' AND-->
<!--                t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                ) or(-->
<!--                t.DEPT_ID=dept.DEP_ORIGIN_ID and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_NAME LIKE CONCAT('%',TRIM(#{insName}),'%') and t.IS_DEL = '0' AND t.IS_SHARE = '1' AND-->
<!--                t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
<!--            <if test="(insName == null or insName == '') and (deptName == null or deptName == '')">-->
                WHERE
                t.IS_DEL = '0' AND t.IS_SHARE = '1' AND t.INS_CODE = #{insCode}
                <include refid="personalPreWhere"/>
                <if test="preOwnerName != null and preOwnerName != ''">
                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')
                </if>
<!--            </if>-->
        </if>
        <if test="shareType != null and shareType.contains('2'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
<!--            <if test="(deptName != null and deptName != '') and ( insName == null or insName == '' )">-->
<!--                ,sys_department as dept-->
<!--                WHERE-->
<!--                (t.IS_DEL = '0' AND t.IS_SHARE = '2' AND t.INS_CODE = #{insCode}-->
<!--                and t.DEPT_ID=dept.DEP_ID-->
<!--                and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                <include refid="personalPreWhere"/>-->
<!--                )-->
<!--                or (t.IS_DEL = '0' AND t.IS_SHARE = '2' AND t.INS_CODE = #{insCode}-->
<!--                and-->
<!--                t.DEPT_ID=dept.DEP_ORIGIN_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                <include refid="personalPreWhere"/>-->
<!--                )-->
<!--            </if>-->

<!--            <if test="(deptName != null and deptName != '') and (insName != null and insName != '') ">-->
<!--                ,sys_department as dept,sys_institution as ins-->
<!--                WHERE-->

<!--                ( t.DEPT_ID=dept.DEP_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                and t.IS_DEL = '0' AND t.IS_SHARE = '2' AND t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID and ins.INS_NAME LIKE-->
<!--                CONCAT('%',TRIM(#{insName}),'%')-->
<!--                ) or-->
<!--                ( t.DEPT_ID=dept.DEP_ORIGIN_ID and dept.DEP_NAME LIKE CONCAT('%',TRIM(#{deptName}),'%')-->
<!--                and t.IS_DEL = '0' AND t.IS_SHARE = '2' AND t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID and ins.INS_NAME LIKE-->
<!--                CONCAT('%',TRIM(#{insName}),'%')-->
<!--                )-->
<!--            </if>-->

<!--            <if test="(insName != null and insName != '') and (deptName == null or deptName == '')">-->
<!--                ,sys_department as dept,sys_institution as ins-->
<!--                WHERE-->
<!--                ( t.DEPT_ID=dept.DEP_ID-->
<!--                and dept.APP_ID=t.DEPT_ID and dept.INS_CODE = t.INS_CODE-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.APP_ID=dept.APP_ID and ins.INS_CODE = dept.INS_CODE-->
<!--                and ins.INS_NAME LIKE CONCAT('%',TRIM(#{insName}),'%') and t.IS_DEL = '0' AND t.IS_SHARE = '2' AND-->
<!--                t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                ) or(-->
<!--                t.DEPT_ID=dept.DEP_ORIGIN_ID and ins.INS_CODE=dept.INS_CODE and dept.APP_ID=ins.APP_ID-->
<!--                and t.INS_CODE = ins.INS_CODE and t.APP_ID = ins.APP_ID-->
<!--                and ins.INS_NAME LIKE CONCAT('%',TRIM(#{insName}),'%') and t.IS_DEL = '0' AND t.IS_SHARE = '2' AND-->
<!--                t.INS_CODE = #{insCode}-->
<!--                <include refid="personalPreWhere"/>-->
<!--                <if test="preOwnerName != null and preOwnerName != ''">-->
<!--                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
<!--            <if test="(insName == null or insName == '') and (deptName == null or deptName == '')">-->

                WHERE
                t.IS_DEL = '0' AND t.IS_SHARE = '2' AND t.INS_CODE = #{insCode}
                <include refid="personalPreWhere"/>
                <if test="preOwnerName != null and preOwnerName != ''">
                    AND t.PRE_OWNER_NAME LIKE CONCAT('%',TRIM(#{preOwnerName}),'%')
                </if>
<!--            </if>-->
        </if>
        <if test="shareType != null and shareType.contains('3'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
            WHERE t.IS_DEL = '0' AND t.IS_SHARE = '3'
            <if test="preOwner != null">
                and t.PRE_OWNER = #{preOwner} AND t.APP_ID = #{appId}
            </if>
            <if test="disName != null and disName != ''">
                AND t.DIS_NAME LIKE CONCAT('%',TRIM(#{disName}),'%')
            </if>
            <include refid="personalPreWhere"/>
        </if>
        ) t
        ORDER BY t.pre_order IS NULL, t.pre_order, t.INSERT_DATE DESC
    </select>

    <sql id="personalPreWhere">
        <if test="preName != null and preName != ''">
            AND t.PRE_NAME LIKE CONCAT('%',TRIM(#{preName}),'%')
        </if>
        <if test="efficacy != null and efficacy != ''">
            AND t.EFFICACY LIKE CONCAT('%',TRIM(#{efficacy}),'%')
        </if>
        <if test="prePy != null and prePy != ''">
            AND t.PRE_PY LIKE CONCAT('%',TRIM(#{prePy}),'%')
        </if>
        <if test="preWb != null and preWb != ''">
            AND t.PRE_WB LIKE CONCAT('%',TRIM(#{preWb}),'%')
        </if>
        <if test="preType != null and preType != '' ">
            AND t.PRE_TYPE = #{preType}
        </if>
        <if test="preMzZy != null and preMzZy != '' ">
            AND t.pre_mz_zy = #{preMzZy}
        </if>
    </sql>

    <select id="getSearchList" parameterType="TPersonalPrescription" resultMap="BaseResultMap">
        select pers.*,
        (SELECT GROUP_CONCAT(i.YPMC_HIS SEPARATOR ',') FROM t_personal_prescription_item i WHERE i.PERS_PRE_ID =
        pers.PERS_PRE_ID ) preItem
        from (
        SELECT t.*
        FROM t_personal_prescription AS t
        JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner}
        WHERE t.PERS_PRE_ID IS NULL
        <if test="shareType != null and shareType.contains('0'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
            JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner}
            WHERE t.IS_DEL = '0' AND t.IS_SHARE = '0' AND t.PRE_OWNER = #{preOwner}
            <if test="filterSelfByIns">
                <if test="appId != null and appId != '' ">
                    AND t.APP_ID = #{appId}
                </if>
                <if test="insCode != null and insCode != '' ">
                    AND t.INS_CODE = #{insCode}
                </if>
            </if>
            <include refid="personalPreWhere"/>
        </if>
        <if test="shareType != null and shareType.contains('1'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
            JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner}
            WHERE t.IS_DEL = '0' AND t.IS_SHARE = '1'
            <if test="filterDept">
                <if test="appId != null and appId != '' ">
                    AND t.APP_ID = #{appId}
                </if>
                <if test="insCode != null and insCode != '' ">
                    AND t.INS_CODE = #{insCode}
                </if>
                <if test="deptId != null and deptId != ''">
                    and a.dept_id = #{deptId}
                </if>
            </if>
            <include refid="personalPreWhere"/>
        </if>
        <if test="shareType != null and shareType.contains('2'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
            JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner}
            WHERE t.IS_DEL = '0' AND t.IS_SHARE = '2'
<!--            <if test="appId != null and appId != '' ">-->
<!--                AND t.APP_ID = #{appId}-->
<!--            </if>-->
<!--            <if test="insCode != null and insCode != '' ">-->
<!--                AND t.INS_CODE = #{insCode}-->
<!--            </if>-->
            <include refid="personalPreWhere"/>
        </if>

        <if test="shareType != null and shareType.contains('4'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
            JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner}
            WHERE t.IS_DEL = '0' AND t.IS_SHARE = '4'
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <include refid="personalPreWhere"/>
        </if>
        <if test="shareType != null and shareType.contains('5'.toString())">
            UNION
            SELECT t.*
            FROM t_personal_prescription AS t
            JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner}
            WHERE t.IS_DEL = '0' AND t.IS_SHARE = '5'

            <include refid="personalPreWhere"/>
        </if>
        ) as pers
        LEFT JOIN t_statistics_prescription_expand e on pers.PERS_PRE_ID = e.pre_id and e.doctor_id = pers.PRE_OWNER
        order by e.pre_num desc
    </select>

    <select id="getNumByPreNameOnOwner" parameterType="TPersonalPrescription" resultType="int">
        select count(*) from t_personal_prescription
        where is_del = '0' and PRE_NAME = #{preName} and PRE_OWNER = #{preOwner}
        <if test="persPreId != null and persPreId != '' ">
            and PERS_PRE_ID != #{persPreId}
        </if>
    </select>

    <select id="getAppPres" parameterType="TPersonalPrescription" resultType="Map">
        SELECT
        '1' AS isAppPre,
        t.PERS_PRE_ID AS preid,
        t.PRE_NAME prename,
        t.EFFICACY effect,
        t.PRE_EXPERT expertName,
        t.ANNOTATION annotation,
        t.IS_MODIFY ismodify,
        concat(IF(t.PRE_EXPERT_TITLE = '1', '国医大师-', IF(t.PRE_EXPERT_TITLE = '2', '国家级名老中医-',
        IF(t.PRE_EXPERT_TITLE = '3', '省级名老中医-', IF(t.PRE_EXPERT_TITLE = '4', '名医-', '')))), t.PRE_EXPERT) AS expert,
        t.PRE_ORIGIN, t.PRE_NUM,
        t.PRE_DESCRIPTION_ID, t.PRE_DESCRIPTION,
        t.PRE_FREQUENCY_ID, t.PRE_FREQUENCY,
        t.PRE_SMOKE_TYPE_ID, t.PRE_SMOKE_TYPE,
        t.PRE_MAT_TYPE, t.STORE_ID,
        t.CAN_APPEND canAppend,
        GROUP_CONCAT(i.YPMC_HIS SEPARATOR ',') AS matnames
        FROM t_personal_prescription AS t
        JOIN t_personal_prescription_item i ON i.PERS_PRE_ID = t.PERS_PRE_ID
        <where>
            t.IS_DEL = '0'
            and t.IS_SHARE = '3'
            and t.APP_ID = #{appId}
            and (ISNULL(t.IS_USE) OR t.IS_USE = 1)
            <if test="preType != null and preType != '' ">
                and t.PRE_TYPE = #{preType}
            </if>
            <if test="disId != null and disId != '' ">
                and t.DIS_ID = #{disId}
            </if>
            <if test="disName != null and disName != '' ">
                and t.DIS_NAME like concat('%',trim(#{disName}),'%')
            </if>
            <if test="symId != null and symId != '' and preType != null and preType == '1'.toString() ">
                and t.SYM_ID = #{symId}
            </if>
            <if test="symName != null and symName != '' ">
                and t.SYM_NAME like concat('%',trim(#{symName}),'%')
            </if>
        </where>
        GROUP BY
        i.PERS_PRE_ID
        order by t.PRE_PY
    </select>


    <select id="getPersonalPresByType" parameterType="TPersonalPrescription" resultMap="BaseResultMap">
        select
        t.*,
        tppdm.id m_id,
        tppdm.PERS_PRE_ID M_PERS_PRE_ID,
        tppdm.DIS_ID m_dis_id,
        tppdm.DIS_NAME m_dis_name,
        tppdm.SYM_ID m_sym_id,
        tppdm.SYM_NAME m_sym_name,
        tppdm.THE_CODE m_the_code,
        tppdm.THE_NAMES m_the_names,
        (SELECT GROUP_CONCAT(i.YPMC_HIS SEPARATOR ',') FROM t_personal_prescription_item i WHERE i.PERS_PRE_ID =
        t.PERS_PRE_ID ) preItem
        FROM t_personal_prescription AS t
        JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner}
        LEFT JOIN t_statistics_prescription_expand e ON t.PERS_PRE_ID = e.pre_id AND e.doctor_id = t.PRE_OWNER
        left join t_personal_prescription_dis_mapping tppdm on(tppdm.PERS_PRE_ID = t.PERS_PRE_ID)
        WHERE t.IS_DEL = '0'
        AND t.IS_SHARE = #{shareType}
        <if test="shareType != null and shareType == '0'.toString() and filterSelfByIns">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                AND t.INS_CODE = #{insCode}
            </if>
        </if>
        <if test="shareType != null and shareType == '1'.toString() and filterDept">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                AND t.INS_CODE = #{insCode}
            </if>
            <if test="deptId != null and deptId != ''">
                and a.dept_id = #{deptId}
            </if>
        </if>
        <if test="shareType != null and shareType == '4'.toString() and filterApp">
            and a.APP_ID = #{appId}
        </if>
        <if test="isOther">
            and t.PERS_PRE_ID not in (select m.PERS_PRE_ID from t_personal_prescription_fmap m )
        </if>
        <include refid="personalPreWhere"/>
        GROUP BY t.PERS_PRE_ID
        ORDER BY t.pre_order IS NULL, t.pre_order, e.pre_num DESC, t.INSERT_DATE DESC
    </select>

    <select id="getPersonalPresByTypeDisMapping" parameterType="TPersonalPrescription" resultMap="BaseResultMap">
        SELECT
        t.*,
        tppdm2.id m_id,
        tppdm2.PERS_PRE_ID M_PERS_PRE_ID,
        tppdm2.DIS_ID m_dis_id,
        tppdm2.DIS_NAME m_dis_name,
        tppdm2.SYM_ID m_sym_id,
        tppdm2.SYM_NAME m_sym_name,
        tppdm2.THE_CODE m_the_code,
        tppdm2.THE_NAMES m_the_names,
        (SELECT GROUP_CONCAT(i.YPMC_HIS SEPARATOR ',') FROM t_personal_prescription_item i WHERE i.PERS_PRE_ID =
        t.PERS_PRE_ID ) preItem,
<!--  计算权重 -->
        (
            (IF((tppdm.`DIS_ID`) =#{disId}, 4, 0)) +
            (IF((tppdm.`SYM_ID`) =#{symId}, 2, 0)) +
            (IF((tppdm.`THE_CODE`) =#{theCode}, 1, 0))
        ) totalScore
        FROM t_personal_prescription AS t
      <!--  JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner} -->
        LEFT JOIN t_statistics_prescription_expand e ON t.PERS_PRE_ID = e.pre_id AND e.doctor_id = t.PRE_OWNER
        left join t_personal_prescription_dis_mapping tppdm on(
        tppdm.PERS_PRE_ID = t.PERS_PRE_ID
        <if test="disId != null and disId != ''">
            and tppdm.DIS_ID = #{disId}
        </if>
        <if test="symId != null and symId != ''">
            and tppdm.SYM_ID = #{symId}
        </if>
        <if test="theCode != null and theCode != ''">
            and tppdm.THE_CODE = #{theCode}
        </if>
        )
        left join t_personal_prescription_dis_mapping tppdm2 on(
        tppdm2.PERS_PRE_ID = t.PERS_PRE_ID
        )
        WHERE t.IS_DEL = '0'
        AND t.IS_SHARE = #{shareType}
        <if test="shareType != null and shareType == '0'.toString() and filterSelfByIns">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                AND t.INS_CODE = #{insCode}
            </if>
        </if>
        <if test="shareType != null and shareType == '0'.toString()">
            <if test="preOwner != null and preOwner != '' ">
                AND t.PRE_OWNER = #{preOwner}
            </if>
        </if>
        <if test="shareType != null and shareType == '1'.toString() and filterDept">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                AND t.INS_CODE = #{insCode}
            </if>
            <if test="deptId != null and deptId != ''">
                and t.dept_id = #{deptId}
            </if>
        </if>
        <if test="shareType != null and shareType == '2'.toString() and filterInsCode">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                AND t.INS_CODE = #{insCode}
            </if>
        </if>

        <if test="shareType != null and shareType == '4'.toString() and filterApp">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
        </if>
        <if test="isOther">
            and t.PERS_PRE_ID not in (select m.PERS_PRE_ID from t_personal_prescription_fmap m )
        </if>
        <include refid="personalPreWhere"/>
        ORDER BY totalScore desc ,t.pre_order IS NULL, t.pre_order, e.pre_num DESC, t.INSERT_DATE DESC
        <if test="page != null">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="countGetPersonalPresByTypeDisMapping" parameterType="TPersonalPrescription" resultType="Long">
        SELECT COUNT(0) FROM (

        SELECT
        t.PERS_PRE_ID
        FROM t_personal_prescription AS t
      <!--  JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner} -->
        LEFT JOIN t_statistics_prescription_expand e ON t.PERS_PRE_ID = e.pre_id AND e.doctor_id = t.PRE_OWNER
        left join t_personal_prescription_dis_mapping tppdm on(
        tppdm.PERS_PRE_ID = t.PERS_PRE_ID
        <if test="disId != null and disId != ''">
            and tppdm.DIS_ID = #{disId}
        </if>
        <if test="symId != null and symId != ''">
            and tppdm.SYM_ID = #{symId}
        </if>
        <if test="theCode != null and theCode != ''">
            and tppdm.THE_CODE = #{theCode}
        </if>
        )
        WHERE t.IS_DEL = '0'
        AND t.IS_SHARE = #{shareType}
        <if test="shareType != null and shareType == '0'.toString() and filterSelfByIns">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                AND t.INS_CODE = #{insCode}
            </if>
        </if>
        <if test="shareType != null and shareType == '0'.toString()">
            <if test="preOwner != null and preOwner != '' ">
                AND t.PRE_OWNER = #{preOwner}
            </if>
        </if>
        <if test="shareType != null and shareType == '1'.toString() and filterDept">
            <if test="appId != null and appId != '' ">
                AND t.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != '' ">
                AND t.INS_CODE = #{insCode}
            </if>
            <if test="deptId != null and deptId != ''">
                and t.dept_id = #{deptId}
            </if>
        </if>
        <if test="isOther">
            and t.PERS_PRE_ID not in (select m.PERS_PRE_ID from t_personal_prescription_fmap m )
        </if>
        <include refid="personalPreWhere"/>
        GROUP BY  t.`PERS_PRE_ID`
) as a
    </select>

    <select id="getPersonalPresByFolder" parameterType="TPersonalPrescription" resultMap="BaseResultMap">
        SELECT t.*,
        tppdm2.id m_id,
        tppdm2.PERS_PRE_ID M_PERS_PRE_ID,
        tppdm2.DIS_ID m_dis_id,
        tppdm2.DIS_NAME m_dis_name,
        tppdm2.SYM_ID m_sym_id,
        tppdm2.SYM_NAME m_sym_name,
        tppdm2.THE_CODE m_the_code,
        tppdm2.THE_NAMES m_the_names,
        (SELECT GROUP_CONCAT(i.YPMC_HIS SEPARATOR ',') FROM t_personal_prescription_item i WHERE i.PERS_PRE_ID =
        t.PERS_PRE_ID ) preItem,
        <!--  计算权重 -->
        (
        (
        CASE
        WHEN  (tppdm.`DIS_ID`) =#{disId}  THEN 4
        ELSE 0
        END
        ) +
        (
        CASE
        WHEN  (tppdm.`SYM_ID`) =#{symId}  THEN 2
        ELSE 0
        END
        ) +
        (
        CASE
        WHEN  (tppdm.`THE_CODE`) =#{theCode}  THEN 1
        ELSE 0
        END
        )

        ) totalScore
        FROM t_personal_prescription AS t
        join t_personal_prescription_fmap m on t.PERS_PRE_ID = m.PERS_PRE_ID
     <!--   JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner} -->
        LEFT JOIN t_statistics_prescription_expand e on t.PERS_PRE_ID = e.pre_id and e.doctor_id = t.PRE_OWNER
        left join t_personal_prescription_dis_mapping tppdm on(tppdm.PERS_PRE_ID = t.PERS_PRE_ID
        <if test="disId != null and disId != ''">
            and tppdm.DIS_ID = #{disId}
        </if>
        <if test="symId != null and symId != ''">
            and tppdm.SYM_ID = #{symId}
        </if>
        <if test="theCode != null and theCode != ''">
            and tppdm.THE_CODE = #{theCode}
        </if>

        )
        left join t_personal_prescription_dis_mapping tppdm2 on(tppdm2.PERS_PRE_ID = t.PERS_PRE_ID)
        <where>
            t.IS_DEL = '0'
            and
            m.FOLDER_ID in
            <foreach collection="folders" index="index" item="node" open="(" close=")" separator=",">
                #{node.folder}
            </foreach>
            <include refid="personalPreWhere"/>
            AND IF(t.IS_SHARE= '0' AND #{filterSelfByIns}, t.INS_CODE = #{insCode}, 1=1)
        </where>
        order by totalScore desc,t.pre_order IS NULL, t.pre_order, e.pre_num desc, t.INSERT_DATE DESC
        <if test="page != null">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="countGetPersonalPresByFolder" parameterType="TPersonalPrescription" resultType="Long">

        SELECT COUNT(0) FROM (
        SELECT t.PERS_PRE_ID
        FROM t_personal_prescription AS t
        join t_personal_prescription_fmap m on t.PERS_PRE_ID = m.PERS_PRE_ID
        <!-- JOIN t_personal_rule_auth a ON a.pers_pre_id = t.PERS_PRE_ID AND a.user_id = #{preOwner} -->
        LEFT JOIN t_statistics_prescription_expand e on t.PERS_PRE_ID = e.pre_id and e.doctor_id = t.PRE_OWNER
        left join t_personal_prescription_dis_mapping tppdm on(tppdm.PERS_PRE_ID = t.PERS_PRE_ID)
        <where>
            t.IS_DEL = '0'
            and
            m.FOLDER_ID in
            <foreach collection="folders" index="index" item="node" open="(" close=")" separator=",">
                #{node.folder}
            </foreach>
            <include refid="personalPreWhere"/>
            AND IF(t.IS_SHARE= '0' AND #{filterSelfByIns}, t.INS_CODE = #{insCode}, 1=1)
        </where>
        GROUP BY  t.`PERS_PRE_ID`
) as a
    </select>

    <select id="personalPrescriptionisModify" parameterType="String" resultType="integer">
        SELECT IS_MODIFY
        FROM t_personal_prescription
        WHERE PERS_PRE_ID = #{id}
    </select>
</mapper>