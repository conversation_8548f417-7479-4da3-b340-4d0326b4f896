<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionFolderMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder">
        <id column="FOLDER_ID" jdbcType="VARCHAR" property="folderId"/>
        <result column="FOLDER_NAME" jdbcType="VARCHAR" property="folderName"/>
        <result column="FOLDER_NUM" jdbcType="INTEGER" property="folderNum"/>
        <result column="FOLDER_TYPE" jdbcType="VARCHAR" property="folderType"/>
        <result column="FOLDER_PID" jdbcType="VARCHAR" property="folderPid"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATE_USERNAME" jdbcType="VARCHAR" property="createUsername"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_USERNAME" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate"/>
        <result column="DEL_USER" jdbcType="VARCHAR" property="delUser"/>
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername"/>
    </resultMap>


    <sql id="Base_Column_List">
    FOLDER_ID,FOLDER_NAME,FOLDER_NUM,FOLDER_TYPE,FOLDER_PID,APP_ID,INS_CODE,DEPT_ID,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,DEL_DATE,DEL_USER,DEL_USERNAME
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPersonalPrescriptionFolder">
        update t_personal_prescription_folder
        set IS_DEL = '1', DEL_DATE = #{delDate}, DEL_USER = #{delUser}, DEL_USERNAME = #{delUsername}
        where FOLDER_ID = #{ folderId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_personal_prescription_folder where FOLDER_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TPersonalPrescriptionFolder">
        insert into t_personal_prescription_folder (<include refid="Base_Column_List"/>) values
        (#{folderId},#{folderName},#{folderNum},#{folderType},#{folderPid},#{appId},#{insCode},#{deptId},#{createDate},#{createUser},#{createUsername},#{updateDate},#{updateUser},#{updateUsername},#{delDate},#{delUser},#{delUsername})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_personal_prescription_folder (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.folderId},#{item.folderName},#{item.folderNum},#{item.folderType},#{item.folderPid},#{item.appId},#{item.insCode},#{item.deptId},#{item.createDate},#{item.createUser},#{item.createUsername},#{item.updateDate},#{item.updateUser},#{item.updateUsername},#{item.delDate},#{item.delUser},#{item.delUsername})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPersonalPrescriptionFolder">
        update t_personal_prescription_folder
        <set>
            <if test="folderName != null">
                FOLDER_NAME = #{ folderName },
            </if>
            <if test="folderNum != null">
                FOLDER_NUM = #{ folderNum },
            </if>
            <if test="folderType != null">
                FOLDER_TYPE = #{ folderType },
            </if>
            <if test="folderPid != null">
                FOLDER_PID = #{ folderPid },
            </if>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="deptId != null">
                DEPT_ID = #{ deptId },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUsername != null">
                CREATE_USERNAME = #{ createUsername },
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{ updateUser },
            </if>
            <if test="updateUsername != null">
                UPDATE_USERNAME = #{ updateUsername },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="delUser != null">
                DEL_USER = #{ delUser },
            </if>
            <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
            </if>
        </set>
        where FOLDER_ID = #{ folderId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_personal_prescription_folder where FOLDER_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPersonalPrescriptionFolder" resultMap="BaseResultMap">
        SELECT FOLDER_ID,FOLDER_NAME,FOLDER_NUM,FOLDER_TYPE,FOLDER_PID,CREATE_USER
        from t_personal_prescription_folder
        <where>
            IS_DEL = '0'
            <if test=" appId != null and appId!='' ">
                and APP_ID = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and INS_CODE = #{insCode}
            </if>
            <if test=" deptId != null and deptId!='' ">
                and DEPT_ID = #{deptId}
            </if>
            <if test=" createUser != null and createUser!='' ">
                and CREATE_USER = #{createUser}
            </if>
            <if test=" folderType != null and folderType!='' ">
                and FOLDER_TYPE = #{folderType}
            </if>
            <if test=" folderName != null and folderName!='' ">
                and FOLDER_NAME like concat('%', #{folderName}, '%')
            </if>
        </where>
        order by FOLDER_NUM, CREATE_DATE desc
    </select>

    <update id="sortFolderNumList" parameterType="list">
        update t_personal_prescription_folder set FOLDER_NUM = case FOLDER_ID
        <foreach collection="list" item="item" close="end">
            when #{item.folderId} then #{item.folderNum}
        </foreach>
        where FOLDER_ID IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.folderId}
        </foreach>
    </update>

</mapper>