<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionFmapMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFmap">
        <id column="FOLDER_ID" jdbcType="VARCHAR" property="folderId"/>
        <result column="PERS_PRE_ID" jdbcType="VARCHAR" property="persPreId"/>
    </resultMap>


    <sql id="Base_Column_List">
    FOLDER_ID,PERS_PRE_ID
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPersonalPrescriptionFmap">
        delete from t_personal_prescription_fmap where FOLDER_ID = #{ folderId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_personal_prescription_fmap where FOLDER_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="TPersonalPrescriptionFmap">
        insert into t_personal_prescription_fmap (FOLDER_ID,PERS_PRE_ID) values
        (#{folderId},#{persPreId})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_personal_prescription_fmap (FOLDER_ID,PERS_PRE_ID) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.folderId},#{item.persPreId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPersonalPrescriptionFmap">
        update t_personal_prescription_fmap
        <set>
            <if test="persPreId != null">
                PERS_PRE_ID = #{ persPreId },
            </if>
        </set>
        where FOLDER_ID = #{ folderId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_personal_prescription_fmap where FOLDER_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPersonalPrescriptionFmap" resultMap="BaseResultMap">
        SELECT FOLDER_ID,PERS_PRE_ID
        from t_personal_prescription_fmap
        <where>
            <if test=" persPreId != null and persPreId!='' ">
                and PERS_PRE_ID = #{persPreId}
            </if>
            <if test=" folderId != null and folderId!='' ">
                and FOLDER_ID = #{folderId}
            </if>
        </where>
    </select>


    <delete id="deleteByFolderId" parameterType="list">
        delete from t_personal_prescription_fmap where FOLDER_ID in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{ item }
        </foreach>
    </delete>

    <delete id="deleteByPersPreId" parameterType="String">
        delete from t_personal_prescription_fmap where PERS_PRE_ID = #{ persPreId }
    </delete>

    <select id="getFolderIdByPerPreId" parameterType="String" resultType="String">
        SELECT m.FOLDER_ID
        from t_personal_prescription_fmap m
        join t_personal_prescription_folder f on f.FOLDER_ID = m.FOLDER_ID and f.is_del = '0'
        where PERS_PRE_ID = #{persPreId}
        limit 1
    </select>

</mapper>