<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionItem">
        <id column="PERS_PRE_ITEM_ID" jdbcType="VARCHAR"  property="persPreItemId" />
        <result column="PERS_PRE_ID" jdbcType="VARCHAR" property="persPreId" />
        <result column="YPML_HIS" jdbcType="VARCHAR" property="ypmlHis" />
        <result column="YPGGDM_HIS" jdbcType="VARCHAR" property="ypggdmHis" />
        <result column="YPML_CENTER" jdbcType="VARCHAR" property="ypmlCenter" />
        <result column="YPGGDM_CENTER" jdbcType="VARCHAR" property="ypggdmCenter" />
        <result column="MAT_ID" jdbcType="VARCHAR" property="matId" />
        <result column="YPDM_HIS" jdbcType="VARCHAR" property="ypdmHis" />
        <result column="YPMC_HIS" jdbcType="VARCHAR" property="ypmcHis" />
        <result column="YPGG_HIS" jdbcType="VARCHAR" property="ypggHis" />
        <result column="BZDW_HIS" jdbcType="VARCHAR" property="bzdwHis" />
        <result column="MAT_DOSE" jdbcType="DECIMAL" property="matDose" />
        <result column="YFID_HIS" jdbcType="VARCHAR" property="yfidHis" />
        <result column="YFMC_HIS" jdbcType="VARCHAR" property="yfmcHis" />
        <result column="MAT_SEQN" jdbcType="INTEGER" property="matSeqn" />
        <result column="ZHUANHUANXS" jdbcType="SMALLINT" property="zhuanhuanxs" />
    </resultMap>


    <sql id="Base_Column_List">
    PERS_PRE_ITEM_ID,PERS_PRE_ID,YPML_HIS,YPGGDM_HIS,YPML_CENTER,YPGGDM_CENTER,MAT_ID,YPDM_HIS,YPMC_HIS,YPGG_HIS,BZDW_HIS,MAT_DOSE,YFID_HIS,YFMC_HIS,MAT_SEQN,ZHUANHUANXS
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPersonalPrescriptionItem">
        delete from t_personal_prescription_item where PERS_PRE_ITEM_ID = #{ persPreItemId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_personal_prescription_item where PERS_PRE_ITEM_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TPersonalPrescriptionItem">
        insert into t_personal_prescription_item (PERS_PRE_ITEM_ID,PERS_PRE_ID,YPML_HIS,YPGGDM_HIS,YPML_CENTER,YPGGDM_CENTER,MAT_ID,YPDM_HIS,YPMC_HIS,YPGG_HIS,BZDW_HIS,MAT_DOSE,YFID_HIS,YFMC_HIS,MAT_SEQN,ZHUANHUANXS) values
        (#{persPreItemId},#{persPreId},#{ypmlHis},#{ypggdmHis},#{ypmlCenter},#{ypggdmCenter},#{matId},#{ypdmHis},#{ypmcHis},#{ypggHis},#{bzdwHis},#{matDose},#{yfidHis},#{yfmcHis},#{matSeqn},#{zhuanhuanxs})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_personal_prescription_item (PERS_PRE_ITEM_ID,PERS_PRE_ID,YPML_HIS,YPGGDM_HIS,YPML_CENTER,YPGGDM_CENTER,MAT_ID,YPDM_HIS,YPMC_HIS,YPGG_HIS,BZDW_HIS,MAT_DOSE,YFID_HIS,YFMC_HIS,MAT_SEQN,ZHUANHUANXS) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.persPreItemId},#{item.persPreId},#{item.ypmlHis},#{item.ypggdmHis},#{item.ypmlCenter},#{item.ypggdmCenter},#{item.matId},#{item.ypdmHis},#{item.ypmcHis},#{item.ypggHis},#{item.bzdwHis},#{item.matDose},#{item.yfidHis},#{item.yfmcHis},#{item.matSeqn},#{item.zhuanhuanxs})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPersonalPrescriptionItem">
        update t_personal_prescription_item
        <set>
            <if test="persPreId != null">
                PERS_PRE_ID = #{ persPreId },
            </if>
            <if test="ypmlHis != null">
                YPML_HIS = #{ ypmlHis },
            </if>
            <if test="ypggdmHis != null">
                YPGGDM_HIS = #{ ypggdmHis },
            </if>
            <if test="ypmlCenter != null">
                YPML_CENTER = #{ ypmlCenter },
            </if>
            <if test="ypggdmCenter != null">
                YPGGDM_CENTER = #{ ypggdmCenter },
            </if>
            <if test="matId != null">
                MAT_ID = #{ matId },
            </if>
            <if test="ypdmHis != null">
                YPDM_HIS = #{ ypdmHis },
            </if>
            <if test="ypmcHis != null">
                YPMC_HIS = #{ ypmcHis },
            </if>
            <if test="ypggHis != null">
                YPGG_HIS = #{ ypggHis },
            </if>
            <if test="bzdwHis != null">
                BZDW_HIS = #{ bzdwHis },
            </if>
            <if test="matDose != null">
                MAT_DOSE = #{ matDose },
            </if>
            <if test="yfidHis != null">
                YFID_HIS = #{ yfidHis },
            </if>
            <if test="yfmcHis != null">
                YFMC_HIS = #{ yfmcHis },
            </if>
            <if test="matSeqn != null">
                MAT_SEQN = #{ matSeqn },
            </if>
            <if test="zhuanhuanxs != null">
                ZHUANHUANXS = #{ zhuanhuanxs },
            </if>
        </set>
        where PERS_PRE_ITEM_ID = #{ persPreItemId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update t_personal_prescription_item set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from t_personal_prescription_item where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_personal_prescription_item where PERS_PRE_ITEM_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_personal_prescription_item where PERS_PRE_ITEM_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="TPersonalPrescriptionItem" resultType="Map">
        SELECT PERS_PRE_ITEM_ID,PERS_PRE_ID,YPML_HIS,YPGGDM_HIS,YPML_CENTER,YPGGDM_CENTER,MAT_ID,YPDM_HIS,YPMC_HIS,YPGG_HIS,BZDW_HIS,MAT_DOSE,YFID_HIS,YFMC_HIS,MAT_SEQN  from t_personal_prescription_item
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TPersonalPrescriptionItem" resultMap="BaseResultMap">
        SELECT PERS_PRE_ITEM_ID,PERS_PRE_ID,YPML_HIS,YPGGDM_HIS,YPML_CENTER,YPGGDM_CENTER,MAT_ID,YPDM_HIS,YPMC_HIS,YPGG_HIS,BZDW_HIS,MAT_DOSE,YFID_HIS,YFMC_HIS,MAT_SEQN,ZHUANHUANXS
        from t_personal_prescription_item
        <where>
            <if test=" name != null and name!='' ">
            </if>
        </where>
    </select>

    <!-- 修改时，先物理删除协定方详情，再新增 -->
    <delete id="deleteByPersPreId" parameterType="String">
       delete from t_personal_prescription_item where PERS_PRE_ID = #{ persPreId }
    </delete>


    <select id="getPersItems" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_personal_prescription_item where PERS_PRE_ID = #{persPreId} order  by MAT_SEQN
    </select>

    <select id="getPerItems" resultType="Map" parameterType="String">
        select
        PERS_PRE_ITEM_ID as itemid,
        PERS_PRE_ID as preid,
        YPML_HIS as ypmlhis,
        YPGGDM_HIS as ypggdmhis,
        YPML_CENTER as ypmlcenter,
        YPGG_HIS as ypgghis,
        YPGGDM_CENTER as ypggdmcenter,
        MAT_ID as matid,
        YPDM_HIS as ypdmHis,
        YPMC_HIS as matname,
        BZDW_HIS as unit,
        MAT_DOSE as dose,
        YFID_HIS as useageId,
        YFMC_HIS as useage,
        MAT_SEQN as seqn,
        ZHUANHUANXS as zhuanhuanxs
        from t_personal_prescription_item where PERS_PRE_ID = #{persPreId} order  by MAT_SEQN
    </select>
</mapper>