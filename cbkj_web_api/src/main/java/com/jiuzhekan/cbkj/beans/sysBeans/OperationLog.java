package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class OperationLog implements Serializable {

    private static final long serialVersionUID = 7413430229048767124L;
    public String logId;

    public String adminName;

    public String adminId;

    public String descr;

    public String params;

    public String results;

    public String httpIp;

    public Date createDate;

    public Integer isOk;

    public String reason;

    public String methodName;

    public String httpMode;

    @ApiModelProperty(value = "")
    private String beginTime;
    @ApiModelProperty(value = "")
    private String endTime;


    private String tableSuffix = "";
}