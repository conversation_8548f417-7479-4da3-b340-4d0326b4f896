package com.jiuzhekan.cbkj.beans.sysBeans;

import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@ApiModel
public class SysAdminPractice implements Serializable {

    @ApiModelProperty(value = "执业ID")
    private Integer practiceId;

    @ApiModelProperty(value = "用户ID")
    private String adminId;

    @ApiModelProperty(value = "医联体ID")
    private String appId;

    @ApiModelProperty(value = "医联体ID")
    private String appName;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    @ApiModelProperty(value = "科室ID")
    private String depId;

    @ApiModelProperty(value = "科室名称")
    private String depName;

    @ApiModelProperty(value = "工号")
    private String employeeId;

    @ApiModelProperty(value = "第三方医生ID")
    private String originDoctorId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    public SysAdminPractice(Object ssoData, String appId, String insCode) {
        if (ssoData instanceof HashMap) {
            Map ssoUser = (HashMap) ssoData;
            Object doctorMultipoint = ssoUser.get("doctorMultipoint");
            if (doctorMultipoint instanceof List) {
                List<Map<String, Object>> doctorMultipointList = (List<Map<String, Object>>) doctorMultipoint;
                for (Map<String, Object> doctor : doctorMultipointList) {
                    String appId1 = doctor.get("appId").toString();
                    String insCode1 = doctor.get("insCode").toString();
                    if (appId.equals(appId1) && insCode.equals(insCode1)) {
                        this.appId = appId1;
                        this.appName = String.valueOf(doctor.get("appName"));
                        this.insCode = insCode1;
                        this.insName = String.valueOf(doctor.get("insName"));
                        this.adminId = String.valueOf(doctor.get("userId"));
                        this.depId = doctor.get("deptId") == null ? Constant.BASIC_DEPT_ID : doctor.get("deptId").toString();
                        this.depName = String.valueOf(doctor.get("deptName"));
                        this.employeeId = String.valueOf(doctor.get("employeeId"));
                        break;
                    }
                }
            }
        }

    }
}
