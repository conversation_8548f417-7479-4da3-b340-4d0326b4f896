package com.jiuzhekan.cbkj.beans.zkxc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 11:45
 * @Version 1.0
 */
@ApiModel
@Data
public class FaceHistoryDetail {

    private Integer collectId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    private String faceOriginalImg;

    @ApiModelProperty(value = "面色")
    private String color;

    @ApiModelProperty(value = "光泽")
    private String light;

    @ApiModelProperty(value = "唇色")
    private String lip;

    @ApiModelProperty(value = "整体描述")
    private String totalDescription;

    @ApiModelProperty(value = "面色标定")
    private String colorCalibration;
    @ApiModelProperty(value = "光泽标定")
    private String lightCalibration;

    @ApiModelProperty(value = "唇色标定")
    private String lipCalibration;







}
