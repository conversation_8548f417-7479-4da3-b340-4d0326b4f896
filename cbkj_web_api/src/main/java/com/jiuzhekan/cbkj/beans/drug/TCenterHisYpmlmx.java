package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("第三方药品目录明细表")
public class TCenterHisYpmlmx implements Serializable{
    @ApiModelProperty(value = "药品目录ID")
    private String ypmlId;
    @ApiModelProperty(value = "第三方药品代码（必须唯一，如果HIS系统需要通过多个字段来确定唯一药品情况，请把各字段通过“-”进行拼接，推送回HIS系统时的药品代码为该值，HIS系统可根据当初拼接进行解析，如：需要通过药名代码、规格代码、产地代码才能确定这味药，拼接成“药名代码-规格代码-产地代码”）")
    private String yaopindm;
    @ApiModelProperty(value = "第三方药品名称")
    private String yaopinmc;
    @ApiModelProperty(value = "规格ID")
    private String guigeid;
    @ApiModelProperty(value = "药品规格")
    private String yaopingg;
    @ApiModelProperty(value = "产地ID")
    private String chandiid;
    @ApiModelProperty(value = "产地名称")
    private String chandimc;
    @ApiModelProperty(value = "剂型")
    private String jixing;
    @ApiModelProperty(value = "批发价")
    private BigDecimal pifajia;
    @ApiModelProperty(value = "零售价")
    private BigDecimal lingshoujia;
    @ApiModelProperty(value = "剂量")
    private String jiliang;
    @ApiModelProperty(value = "剂量单位")
    private String jiliangdw;
    @ApiModelProperty(value = "一次剂量")
    private String yicijl;
    @ApiModelProperty(value = "一次剂量单位")
    private String yicijldw;
    @ApiModelProperty(value = "包装量")
    private Short baozhuangliang;
    @ApiModelProperty(value = "包装单位")
    private String baozhuangdw;
    @ApiModelProperty(value = "中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂）")
    private Byte zhongyaolx;
    @ApiModelProperty(value = "饮片转成颗粒的转换系数")
    private Double zhuanhuanxs;
    @ApiModelProperty(value = "修改时间(药品名称、规格、单位修改时更新该时间，需要同步处理 中心药房和HIS的药品映射关系)")
    private Date xiugaisj;
    @ApiModelProperty(value = "作废标志（0可用，1作废）")
    private Byte zuofeibz;
    @ApiModelProperty(value = "拼音码")
    private String shuruma1;
    @ApiModelProperty(value = "五笔码")
    private String shuruma2;
    @ApiModelProperty(value = "统一药品代码（由开方系统生成）")
    private String yaopindmTy;
    @ApiModelProperty(value = "能否医保 0自费 1医保")
    private String isInsurance;
    @ApiModelProperty(value = "日最大剂量（内服）")
    private Double dailyMaxDoseIn;
    @ApiModelProperty(value = "日最大剂量（外用）")
    private Double dailyMaxDoseExt;
    @ApiModelProperty(value = "医保日最大开药量（制剂）")
    private Double dailyMaxNumPrep;
    @ApiModelProperty(value = "药品用法")
    private String yaopinyf;
    @ApiModelProperty(value = "单独使用时不予支付（1不支持）")
    private String notPayAlone;
    @ApiModelProperty(value = "不纳入基金支付范围（1不支持）")
    private String notPayInFund;
    @ApiModelProperty(value = "频次ID")
    private String frequencyId;
    @ApiModelProperty(value = "频次")
    private String frequency;
    @ApiModelProperty(value = "频次系数（次数/天）")
    private String frequencyRate;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "功能主治")
    private String effect;
    @ApiModelProperty(value = "用法用量")
    private String usageDesc;
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;
    @ApiModelProperty(value = "不适应症")
    private String medicontrolText;
    @ApiModelProperty(value = "上级药品代码")
    private String parentYpdm;

    private String appId;
    private BigDecimal stockNum;

    private Integer page;
    private Integer limit;

    private String toxicityOverdoseMultiple; //毒药倍数
    private String externalUseOnly; //仅限外用(0关1开)
    // @ApiModelProperty(value = "最大剂量")
    private Double maxdose;

    // @ApiModelProperty(value = "最大剂量")s
    private Double mindose;


    

}