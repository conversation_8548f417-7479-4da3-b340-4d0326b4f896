package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel("处方代煎")
public class TPreDecoction implements Serializable{

    @ApiModelProperty(value = "处方ID")
    private String preId;

    @ApiModelProperty(value = "代煎贴数")
    private Integer decoctNum;

    @ApiModelProperty(value = "煎药方式ID")
    private String dicId;

    @ApiModelProperty(value = "煎药方式代码")
    private String dicCode;

    @ApiModelProperty(value = "煎药方式名称")
    private String dicName;

    @ApiModelProperty(value = "煎药方式收费代码")
    private String chargeCode;

    @ApiModelProperty(value = "煎药方式收费项目名称")
    private String chargeName;

    @ApiModelProperty(value = "价格")
    private Double price;


}
