package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TRecord2Look implements Serializable{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "病历编号")
    private String recId;

    @ApiModelProperty(value = "标签编号")
    private String tagNo;

    @ApiModelProperty(value = "舌代码")
    private String wzsdm;

    @ApiModelProperty(value = "舌其他")
    private String wzsqt;

    @ApiModelProperty(value = "舌")
    private String wzs;

    @ApiModelProperty(value = "苔代码")
    private String wztdm;

    @ApiModelProperty(value = "苔其他")
    private String wztqt;

    @ApiModelProperty(value = "苔")
    private String wzt;

    @ApiModelProperty(value = "神")
    private String wzws;
    private String wzwsdm;

    @ApiModelProperty(value = "神其他")
    private String wzwsqt;

    @ApiModelProperty(value = "面")
    private String wzwms;
    private String wzwmsdm;

    @ApiModelProperty(value = "面其他")
    private String wzwmsqt;

    @ApiModelProperty(value = "形态")
    private String wzwxt;
    private String wzwxtdm;

    @ApiModelProperty(value = "形态其他")
    private String wzwxtqt;

    @ApiModelProperty(value = "头颅五官九窍")
    private String wzwtlwgjq;
    private String wzwtlwgjqdm;

    @ApiModelProperty(value = "头颅五官九窍其他")
    private String wzwtlwgjqqt;

    @ApiModelProperty(value = "皮肤")
    private String wzwpf;
    private String wzwpfdm;

    @ApiModelProperty(value = "皮肤其他")
    private String wzwpfqt;

    @ApiModelProperty(value = "络脉")
    private String wzwlm;
    private String wzwlmdm;

    @ApiModelProperty(value = "络脉其他")
    private String wzwlmqt;

    @ApiModelProperty(value = "排泄物与分泌物")
    private String wzwpxwyfmw;
    private String wzwpxwyfmwdm;

    @ApiModelProperty(value = "排泄物与分泌物其他")
    private String wzwpxwyfmwqt;

    @ApiModelProperty(value = "其他")
    private String wzqt1;

    @ApiModelProperty(value = "状态【0:可用，1:禁用】")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "望小儿胸腹")
    private String wzwxexf;
    @ApiModelProperty(value = "望小儿胸腹代码")
    private String wzwxexfdm;
    private String wzwxexfqt;

    private Integer equipmentPatientId;
}
