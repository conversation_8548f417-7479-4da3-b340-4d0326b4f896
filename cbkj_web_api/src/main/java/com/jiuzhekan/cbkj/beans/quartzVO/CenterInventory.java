package com.jiuzhekan.cbkj.beans.quartzVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created By xhq on  2020/2/20 9:18
 * @description : 此类用于接收药品库存同步的药品信息
 */
@Data
public class CenterInventory  implements Serializable {
    private String ypmlId;       //药品目录id(由开方系统分配给中心药房)
    private String YINGYONGID;  //处方单号
    private String KUCUNSL;
    private String XUKUCSL;
    private String JIAGEID;
    private String YAOPINID;
    private String YAOPINMC;
    private String GUIGEID;
    private String YAOPINGG;
    private String CHANDIID;
    private String CHANDIMC;
    private String JIXING;
    private String PIFAJIA;
    private String LINGSHOUJIA;
    private String JINJIA;
    private String PIZHUNWH;
    private String SHURUMA1;
    private String SHURUMA2;
    private String ZHANGBULB;
    private String JILIANG;
    private String JILIANGDW;
    private String YICIJL;
    private String YICIJLDW;
    private String ZUIXIAODW;
    private String BAOZHUANGLIANG;
    private String BAOZHUANGDW;
    private String ZHONGYAOLX;
    private String ZHUANHUANDW;
    private String ZHUANHUANBL;
    private String TINGYONGBZ;
    private String ZUOFEIBZ;
    private String XIUGAISJ;
    private String LINGSHOUDA;              // 无用
    private String YUKOUKCSL;
    private String YAOPINYF;

    private String kcId;            // 开方生成的库存id
    private String storeName;       // 开方生成的药房名称
    private String yaopindmTy;       // 开方生成的统一药品代码
 }