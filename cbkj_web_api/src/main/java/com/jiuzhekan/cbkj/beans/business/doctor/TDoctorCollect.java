package com.jiuzhekan.cbkj.beans.business.doctor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel
public class TDoctorCollect implements Serializable{

    @ApiModelProperty(value = "")
    private String collectId;

    @ApiModelProperty(value = "收藏模块:1名医验案 2中药 3方剂 4经络穴位 5疾病 6中成药 7舌诊 8脉象 9临床诊疗 10书籍 11经方查询 13中医古籍 12 共享文献 13 中医非药物疗法 ")
    private Integer collectModule;

    @ApiModelProperty(value = "关联id:例如 药品id,方剂id")
    private String connectId;

    @ApiModelProperty(value = "收藏者的ID")
    private String createUser;

//    @ApiModelProperty(value = "收藏者的名字")
//    private String collectUsername;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private Date updateDate;
}