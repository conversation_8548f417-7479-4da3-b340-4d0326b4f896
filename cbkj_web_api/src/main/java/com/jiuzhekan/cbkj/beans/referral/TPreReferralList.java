package com.jiuzhekan.cbkj.beans.referral;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/24 13:25
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@ApiModel
public class TPreReferralList {
    @ApiModelProperty(value = "主键id")
    private Long preReferralId;

    @ApiModelProperty(value = "医工体id")
    private String appId;



    @ApiModelProperty(value = "机构代码")
    private String insCode;

    @ApiModelProperty(value = "机构名称")
    private String insName;

    @ApiModelProperty(value = "医生id")
    private String doctorId;

    @ApiModelProperty(value = "医生姓名")
    private String doctorName;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "疾病-证型")
    private String disNameAndSymName;

    @ApiModelProperty(value = "转诊原因")
    private String referralResult;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "转诊提交时间")
    private Date referralTime;
}
