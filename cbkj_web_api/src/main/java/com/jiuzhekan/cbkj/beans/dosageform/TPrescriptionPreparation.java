package com.jiuzhekan.cbkj.beans.dosageform;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TPrescriptionPreparation implements Serializable{

    @ApiModelProperty(value = "")
    private String preparationId;

    @ApiModelProperty(value = "每日剂数",required = true)
    private String dailyDosage;

    @ApiModelProperty(value = "每日制剂次数",required = true)
    private String dailyPreparations;

    @ApiModelProperty(value = "每日加工量",required = true)
    private String dailyProcessing;

    @ApiModelProperty(value = "每次用量",required = true)
    private String eachTime;

    @ApiModelProperty(value = "处方id",required = true)
    private String preId;

    @ApiModelProperty(value = "处方no")
    private String preNo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "剂型id",required = true)
    private String itemId;

    @ApiModelProperty(value = "剂型名称",required = true)
    private String itemName;

    @ApiModelProperty(value = "所有条目价格相加结果值")
    private BigDecimal preparationFinalPrice;

    @ApiModelProperty(value = "处方制剂收费明细")
    private List<TPrescriptionPreparationCost> costList;
}
