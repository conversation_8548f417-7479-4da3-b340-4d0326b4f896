package com.jiuzhekan.cbkj.beans.business.record;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel(value = "病历从表")
public class TRecordDetail implements Serializable {
    @ApiModelProperty(value = "病历明细ID")
    private String recDetailId;
    @ApiModelProperty(value = "病历ID")
    private String recId;
    @ApiModelProperty(value = "中医电子病历模版ID")
    private String templId;
    @ApiModelProperty(value = "中医电子病历模版明细ID")
    private String detailId;
    @ApiModelProperty(value = "明细名称")
    private String detailName;
    @ApiModelProperty(value = "明细类型(1为标题，2为输入框，3为单选框，4为复选框)")
    private String detailType;
    @ApiModelProperty(value = "明细内容")
    private String detailContent;
    @ApiModelProperty(value = "序号")
    private Integer detailNum;

}