package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TPrescriptionTechnical implements Serializable{

    @ApiModelProperty(value = "")
    private String technicalId;

    @ApiModelProperty(value = "")
    private String preId;

    @ApiModelProperty(value = "项目针数数量")
    private Short projectAcuNum;

    @ApiModelProperty(value = "天数")
    private Short projectDayNum;

    @ApiModelProperty(value = "项目类型id")
    private String projectTypeId;

    @ApiModelProperty(value = "项目类型名称")
    private String projectTypeName;

    @ApiModelProperty(value = "收费目录明细ID")
    private String projectFreeId;

    @ApiModelProperty(value = "收费项目名称")
    private String projectFreeName;

    @ApiModelProperty(value = "收费目录项目代码")
    private String projectFreeCode;

    @ApiModelProperty(value = "单价")
    private Double projectFeePrice;

    @ApiModelProperty(value = "单位")
    private String projectFeeUnit;

    @ApiModelProperty(value = "医保分类(1.甲、2.乙)")
    private String fedicalInsurance;

    @ApiModelProperty(value = "项目内涵")
    private String projectConnotation;

    @ApiModelProperty(value = "限制支付范围")
    private String limitPayment;

    @ApiModelProperty(value = "穴位必填（1：必填，0不必填）")
    private String isAcupoint;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "序号")
    private Integer acuSenq;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}
