package com.jiuzhekan.cbkj.beans.business.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zbh on 2022/3/14 13:02
 *
 * @description： 机构药房配置映射表返回信息
 */
@Data
@ApiModel("药房配置")
public class TDisplayMappingResponse implements Serializable {


    private String displayId;

    @ApiModelProperty(name = "显示名称")
    private String displayName;

    @ApiModelProperty(name = "中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂 8 中成药）")
    private String matType;

    @ApiModelProperty(" 药房ID(一种剂型只能对应一个药房)")
    private String phaId;

    @ApiModelProperty("  药房名称")
    private String phaName;

    @ApiModelProperty(" 药品目录id")
    private String drugId;


    @ApiModelProperty(" 代煎信息list")
    private List<TDisplayDecoction> decoctionList;

    @ApiModelProperty(" 药房费用配置信息list")
    private List<TDisplayExpress> expressList;

    @ApiModelProperty(" 药房制膏信息list")
    private List<TDisplayProduction> productionList;

    @ApiModelProperty("药房通用信息")
    private TDisplayCurrency currency;

    @ApiModelProperty("剂型信息")
    private TDisplayDosageForm dosageForm;


    @ApiModelProperty(" 代煎信息")
    private TDisplayDecoction decoction;

    @ApiModelProperty(" 药房费用配置信息")
    private TDisplayProduction production;

    @ApiModelProperty(" 药房制膏信息")
    private TDisplayExpress express;


    @ApiModelProperty(" 门诊,住院（1门诊，2住院）")
    private String outpatientOrHospitalization;

    @ApiModelProperty(" 是否为默认项(暂时没用，通过参数配置顺序默认第一个)")
    private String isDefault;
}
