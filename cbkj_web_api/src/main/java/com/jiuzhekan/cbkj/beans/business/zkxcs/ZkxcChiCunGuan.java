package com.jiuzhekan.cbkj.beans.business.zkxcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcChiCunGuan implements Serializable{

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "")
    private boolean collectedData;

    @ApiModelProperty(value = "")
    private Float jieLv;

    @ApiModelProperty(value = "")
    private String jieLvStandard;

    @ApiModelProperty(value = "")
    private Float jinZhangDu;

    @ApiModelProperty(value = "")
    private String jinZhangDuStandard;

    @ApiModelProperty(value = "")
    private Float liuLiDu;

    @ApiModelProperty(value = "")
    private String liuLiDuStandard;

    @ApiModelProperty(value = "")
    private Float maiLi;

    @ApiModelProperty(value = "")
    private Float maiLiDeviation;

    @ApiModelProperty(value = "")
    private Float maiLiDownLineValue;

    @ApiModelProperty(value = "")
    private String maiLiStandard;

    @ApiModelProperty(value = "")
    private Float maiLiUpLineValue;

    @ApiModelProperty(value = "")
    private Float maiLv;

    @ApiModelProperty(value = "")
    private Float maiLvDeviation;

    @ApiModelProperty(value = "")
    private Float maiLvDownLineValue;

    @ApiModelProperty(value = "")
    private String maiLvStandard;

    @ApiModelProperty(value = "")
    private Float maiLvUpLineValue;

    @ApiModelProperty(value = "")
    private Float maiWei;

    @ApiModelProperty(value = "")
    private Float maiWeiDeviation;

    @ApiModelProperty(value = "")
    private Float maiWeiDownLineValue;

    @ApiModelProperty(value = "")
    private String maiWeiStandard;

    @ApiModelProperty(value = "")
    private Float maiWeiUpLineValue;

    @ApiModelProperty(value = "")
    private String optPulse1;

    @ApiModelProperty(value = "对应脉象特征描述")
    private String pulsePotential;

    @ApiModelProperty(value = "")
    private Float veinElementDownLineValue;

    @ApiModelProperty(value = "")
    private Float veinElementUpLineValue;

    @ApiModelProperty(value = "")
    private String veinElements;

    @ApiModelProperty(value = "")
    private Integer mzId;

    @ApiModelProperty(value = "1.左尺2.左寸3.左关4.右尺5.右寸6.右关")
    private Integer mzResultType;
    @ApiModelProperty(value = "脉位特征")
    private String maiweiFeatureDesc;

    @ApiModelProperty(value = "脉率特征")
    private String mailvFeatureDesc;

    @ApiModelProperty(value = "节律特征")
    private String jielvFeatureDesc;

    @ApiModelProperty(value = "脉力特征")
    private String mailiFeatureDesc;

    @ApiModelProperty(value = "紧张度特征")
    private String jinzhangduFeatureDesc;
    @ApiModelProperty(value = "流利度特征")
    private String liuliduFeatureDesc;
    private String pulsePotentialCalibration;



}
