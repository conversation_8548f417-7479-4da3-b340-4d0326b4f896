package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 权限
 */
@Data
@ApiModel(value = "菜单")
public class AdminMenu implements Serializable {

    private static final long serialVersionUID = 1654318301580927318L;
    @ApiModelProperty(value = "菜单id")
    private Integer mid;

    @ApiModelProperty(value = "菜单名称")
    private String mname;

    @ApiModelProperty(value = "过滤路径")
    private String url;

    @ApiModelProperty(value = "请求路径")
    private String path;

    @ApiModelProperty(hidden = true)
    private String iconcls;

    @ApiModelProperty(value = "状态 （1、禁用  2、启用）")
    private Integer enabled;

    @ApiModelProperty(value = "上级菜单id")
    private Integer parentMid;

    @ApiModelProperty(hidden = true)
    private Date createDate;

    @ApiModelProperty(hidden = true)
    private String cteateId;

    @ApiModelProperty(hidden = true)
    private String enabledS;

    @ApiModelProperty("1:菜单 2：按钮")
    private String menuType;

    @ApiModelProperty(hidden = true)
    private String btnClass;

    @ApiModelProperty("按钮类型")
    private String btnType;

    @ApiModelProperty(hidden = true)
    private String btnWeight;

    @ApiModelProperty("排序")
    private int sortNumber;

    @ApiModelProperty(hidden = true)
    private String iconCls;

    @ApiModelProperty("打开方式（1. 打开内部页面地址 2. 打开外部地址 3. 执行函数方法）")
    private String openType;

    @ApiModelProperty("子菜单")
    private List<AdminMenu> childList;

    @ApiModelProperty(hidden = true)
    private List<AdminRule> rules;

    @ApiModelProperty(hidden = true)
    private boolean first = false;

    @ApiModelProperty(hidden = true)
    private String ids;

}