package com.jiuzhekan.cbkj.beans.business.doctor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel
public class TDoctorDisease implements Serializable{

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "医生ID")
    private String docId;

    @ApiModelProperty(value = "分组ID")
    private String groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "疾病ID")
    private String disId;

    @ApiModelProperty(value = "疾病名称")
    private String disName;

    @ApiModelProperty(value = "疾病数量")
    private Integer disNum;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "疾病性别")
    private String gender;

}
