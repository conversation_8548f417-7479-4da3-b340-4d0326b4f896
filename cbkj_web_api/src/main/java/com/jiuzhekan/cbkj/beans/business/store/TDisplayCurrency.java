package com.jiuzhekan.cbkj.beans.business.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("药房配置通用信息表")
public class TDisplayCurrency {
    @ApiModelProperty("配置ID")
    private String displayCurrencyId;

    @ApiModelProperty("displayId")
    private String displayId;
    /**
     * 是否预扣计算 0否1是
     */
    @ApiModelProperty("是否预扣计算 0否1是")
    private String withholdSwitch;
    /**
     * 处方保存接口校验库存开关
     */
    @ApiModelProperty("处方保存接口校验库存开关")
    private String preStockSwitch;

    @ApiModelProperty(value = "药品开方多规格下拉开关 1是 0否")
    private Integer manySpeSwitch;

    @ApiModelProperty(value = "内服服药时间开关 0关 1开，默认开")
    private Integer oralMedicationTimeSwitch;

    @ApiModelProperty(value = "加急标志 0 关 1开")
    private Integer urgentSign;
}
