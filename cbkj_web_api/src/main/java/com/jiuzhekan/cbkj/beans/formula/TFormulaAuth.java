package com.jiuzhekan.cbkj.beans.formula;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TFormulaAuth implements Serializable{

    @ApiModelProperty(value = "配方权限ID")
    private Integer authId;

    @ApiModelProperty(value = "配方ID")
    private String formulaId;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "药房ID")
    private String storeId;

    @ApiModelProperty(value = "库存")
    private Integer preStock;

    @ApiModelProperty(value = "新增时间")
    private Date createDate;

    @ApiModelProperty(value = "新增人")
    private String createUser;

}
