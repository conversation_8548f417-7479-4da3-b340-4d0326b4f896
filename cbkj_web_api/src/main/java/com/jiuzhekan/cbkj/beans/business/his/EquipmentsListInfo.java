package com.jiuzhekan.cbkj.beans.business.his;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class EquipmentsListInfo {
    private Integer equipmentPatientId;
    @ApiModelProperty(value = "（新华：以下字段是） -1.待分配设备 0.等待设备数据 1.获取到设备数据 2.已推送给HIS  10.推送失败")
    private Integer infoStatus;
//    @ApiModelProperty(value = "推送失败原因")
//    private String failInfo;
    private String macAddress;

    //    @ApiModelProperty(value = "登记日期")
//    private Date orderTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "报告时间")
    private Date reportTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送报告时间")
    private Date sendTime;

    @ApiModelProperty(value = "1.四诊仪2.经络仪3.热成像仪4.脉象复现仪")
    private Integer equipmentType;

    @ApiModelProperty("消息推送各个地方的的状态")
    private List<EquipmentPushIfo> send;
}
