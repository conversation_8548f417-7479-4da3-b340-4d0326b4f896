package com.jiuzhekan.cbkj.beans.business.record;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel
public class TPrescriptionEvaluation implements Serializable {

    @ApiModelProperty(value = "处方测评ID")
    private String evaId;

    @ApiModelProperty(value = "处方ID")
    private String preId;

    @ApiModelProperty(value = "处方测评类型（1安全用药测评  2经方相似度测评）")
    private String evaType;

    @ApiModelProperty(value = "慎禁忌", required = true)
    private String evaSjj;

    @ApiModelProperty(value = "孕妇慎禁忌", required = true)
    private String evaYfsjj;

    @ApiModelProperty(value = "十八反", required = true)
    private String evaSbf;

    @ApiModelProperty(value = "十九畏", required = true)
    private String evaSjw;

    @ApiModelProperty(value = "用药不宜", required = true)
    private String evaBy;

    @ApiModelProperty(value = "服药饮食禁忌", required = true)
    private String evaYsjj;

    @ApiModelProperty(value = "药物毒性说明", required = true)
    private String evaDxsm;

    @ApiModelProperty(value = "病证用药禁忌", required = true)
    private String evaBzjj;

    @ApiModelProperty(value = "剂量超标", required = true)
    private String evaJlcb;

    @ApiModelProperty(value = "计量偏低", required = true)
    private String evaJlpd;

    @ApiModelProperty(value = "计量超规定", required = true)
    private String evaJlcgd;

    @ApiModelProperty(value = "炮制品", required = true)
    private String evaPzp;

    @ApiModelProperty(value = "多规格同时开", required = true)
    private String evaDgg;


    @ApiModelProperty(value = "十八反十九畏（可能还有其他的类型）药品ID", required = true)
    private String matsbfsjwIds;

    @ApiModelProperty(value = "毒药超剂量", required = true)
    private String evaDycjl;

}