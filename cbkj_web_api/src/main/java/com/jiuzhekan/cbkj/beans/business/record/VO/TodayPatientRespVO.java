package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created By xhq on  2019/12/20 21:14
 */
@Data
@ApiModel(value = "今日病人响应参数VO类")
public class TodayPatientRespVO implements Serializable {


    @ApiModelProperty(value = "挂号ID（时间戳）")
    private String registerId;
    @ApiModelProperty(value = "病历id")
    private String recId;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "就诊科室ID")
    private String deptId;
    @ApiModelProperty(value = "就诊科室名称")
    private String deptName;
    @ApiModelProperty(value = "就诊医生ID")
    private String doctorId;
    @ApiModelProperty(value = "挂号时间('')")
    private Date registerTime;
    @ApiModelProperty(value = "APPID")
    private String appId;
    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "就诊医生姓名")
    private String doctorName;
    @ApiModelProperty(value = "就诊号")
    private String registerNum;
    @ApiModelProperty(value = "挂号时间段（1上午  2下午）")
    private String registerTimeArange;
    @ApiModelProperty(value = "就诊状态（0挂号未缴费，2待就诊，4已就诊，5已付款，6已退款，8已发药）")
    private String registerDiagnosisStatus;
    @ApiModelProperty(value = "门诊类型ID")
    private String clinicTypeId;
    @ApiModelProperty(value = "门诊类型名称")
    private String clinicTypeName;
    @ApiModelProperty(value = "挂号费")
    private String clinicTypeMoney;
    @ApiModelProperty(value = "开方系统打开时间(修改处方时填，新开处方不填)")
    private Date recOpenTime;
    @ApiModelProperty(value = "PATIENT_ID")
    private String patientId;

    @TianAnDecryptField
    @ApiModelProperty(value = "手机号")
    private String patientMobile;

    @ApiModelProperty(value = "证件类型")
    private String patientCertificateType;

    @TianAnDecryptField
    @ApiModelProperty(value = "身份证号")
    private String patientCertificate;

    @ApiModelProperty(value = "出生日期")
    private Date patientBirthday;
    @ApiModelProperty(value = "年龄")
    private String age;

    @TianAnDecryptField
    @ApiModelProperty(value = "性别")
    private String patientGender;
    @ApiModelProperty(value = "就诊卡号")
    private String medicalCardNo;
    @ApiModelProperty(value = "第三方患者ID")
    private String originPatientId;

    @TianAnDecryptField
    @ApiModelProperty(value = "省")
    private String patientCounty;

    @TianAnDecryptField
    @ApiModelProperty(value = "市")
    private String patientTown;

    @TianAnDecryptField
    @ApiModelProperty(value = "区")
    private String patientVillage;

    @TianAnDecryptField
    @ApiModelProperty(value = "街道")
    private String patientStreet;

    @TianAnDecryptField
    @ApiModelProperty(value = "地址")
    private String patientAddress;

    @ApiModelProperty(value = "默认收货地址ID")
    private String dcAddressId;
    @ApiModelProperty(value = "HIS患者就诊序号")
    private String visitNo;
    @ApiModelProperty(value = "患者是否怀孕（Y为是，N为否）")
    private String gravidity;

}