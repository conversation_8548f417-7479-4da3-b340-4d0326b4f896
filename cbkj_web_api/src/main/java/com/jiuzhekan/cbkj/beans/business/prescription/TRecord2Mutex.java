package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TRecord2Mutex implements Serializable{

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "中医疾病Id")
    private String disId;

    @ApiModelProperty(value = "中医疾病名称")
    private String disName;

    @ApiModelProperty(value = "父ID")
    private String parentId;

    @ApiModelProperty(value = "互斥症状名称")
    private String mutexName;

    @ApiModelProperty(value = "子代码")
    private String childCode;

    @ApiModelProperty(value = "互斥子数据")
    private String mutexSubData;

    @ApiModelProperty(value = "状态【0:可用，1:禁用】")
    private String status;


}
