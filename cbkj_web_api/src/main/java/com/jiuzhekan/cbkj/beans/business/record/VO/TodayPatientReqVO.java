package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created By xhq on  2019/12/20 21:14
 */
@Data
public class TodayPatientReqVO implements Serializable {
    private String time;
    private String recDiagnosisStatus;
    private String orderName; // 病人信息查询(0 病人姓名,1 就诊卡号,2 病人手机号,3 病人身份证号，4his就诊号)
    private String orderValue;

    private String startDate;
    private String endDate;
    private String disId;
    private String disName;
    private String symId;
    private String symName;
    private String appId;
    private String insCode;
    private String docId;
    private String docName;
    private String patientId;
//    private String patientName;
    private String patientPy;
    private String patientWb;
    private String recTreType;     // 病历类型（分两类：门诊、住院医嘱，对应值分别为1、2）
    private String visitNo;
    private String currentExtContent;// 扩展内容(输入码:1拼音 2五笔)

    @TianAnEncryptField
    private String patientName;
    @TianAnEncryptField
    private String patientMobile;

    @TianAnEncryptField
    private String patientCertificate;

    private List<Integer> tableHistoryYears;
 }