package com.jiuzhekan.cbkj.beans.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 14:15
 * @Version 1.0
 */
@Data
@ApiModel
public class MZHistoryDAO {
    private Date insertTime;

    @ApiModelProperty(value = "顺序是：左手寸关尺，右手寸关尺,的id主键")
    private String  zkxcChiCunGuanIds;
    private String mzResultType;
    @ApiModelProperty(value = "顺序是：左手的 寸关尺，右手的 寸关尺")
    private String pulsePotentials;
}
