package com.jiuzhekan.cbkj.beans.business.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class TRecordTemplateDetail implements Serializable {

    @ApiModelProperty(value = "中医电子病历模版明细ID")
    private String detailId;

    @ApiModelProperty(value = "中医电子病历模版ID")
    private String templId;

    @ApiModelProperty(value = "模版明细名称")
    private String detailName;

    @ApiModelProperty(value = "模版明细分类/代码（详见本地字典：病历分类/病历代码）")
    private String detailType;

    @ApiModelProperty(value = "性别标志(1男 2女 3全部)")
    private String isSex;

    @ApiModelProperty(value = "模版明细显示形式(1为标题，2为单行输入框，3为多行输入框，4为单选，5为多选，6为 单选 加 选项填入输入框，7为多选 加 选项填入输入框，8为单选加文本，9为多选加文本 10选项")
    private Integer detailDisplay;

    @ApiModelProperty(value = "文本名称")
    private String detailText;

    @ApiModelProperty(value = "默认值")
    private String detailDefault;

    @ApiModelProperty(value = "提示语")
    private String detailHint;

    @ApiModelProperty(value = "备选项内容")
    private String detailContent;

    @ApiModelProperty(value = "内容后缀")
    private String detailSuffix;

    @ApiModelProperty(value = "是否默认(0不默认 1默认)")
    private String isDefault;

    @ApiModelProperty(value = "是否显示(0不显示 1显示")
    private String isShow;

    @ApiModelProperty(value = "是否与其他项互斥(0不互斥 1互斥)")
    private String isMutex;

    @ApiModelProperty(value = "是否必填")
    private String isRequired;

    @ApiModelProperty(value = "疾病ID")
    private String disId;

    @ApiModelProperty(value = "父明细ID")
    private String detailPid;

    @ApiModelProperty(value = "等级")
    private Integer detailLevel;

    @ApiModelProperty(value = "序号")
    private Integer detailNum;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private String createUser;

    @ApiModelProperty(value = "")
    private String createUsername;

    @ApiModelProperty(value = "")
    private Date updateDate;

    @ApiModelProperty(value = "")
    private String updateUser;

    @ApiModelProperty(value = "")
    private String updateUsername;

    @ApiModelProperty(value = "")
    private Date delDate;

    @ApiModelProperty(value = "")
    private String delUser;

    @ApiModelProperty(value = "")
    private String delUsername;

    @ApiModelProperty(value = "")
    private String isDel;


    /*************EXT*************/
    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "是否是子节点")
    private boolean isLeaf;

    @ApiModelProperty(value = "子明细")
    private List<TRecordTemplateDetail> children;

    @ApiModelProperty(value = "选项")
    private List<TRecordTemplateDetail> options;

    private String recId;
}
