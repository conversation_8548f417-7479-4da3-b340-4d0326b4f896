package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date $ $
 **/
@Data
@NoArgsConstructor
@ApiModel
public class TRecord2DicPNew implements Serializable {
    private String version;
    private List<TRecord2DicNew> diagnosis;
    @ApiModelProperty(value = "互斥id编号")
    private Map<String,Object> defaultValue;
}
