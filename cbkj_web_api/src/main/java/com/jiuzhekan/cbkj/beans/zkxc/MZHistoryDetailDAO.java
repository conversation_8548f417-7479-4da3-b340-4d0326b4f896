//package com.jiuzhekan.cbkj.beans.zkxc;
//
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.util.Date;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2024/6/20 14:15
// * @Version 1.0
// */
//@Data
//@ApiModel
//public class MZHistoryDetailDAO {
//    private Date insertTime;
//    @ApiModelProperty(value = "左手和右手寸关尺信息的主键id：顺序是：左手寸关尺，右手寸关尺")
//    private String zkxcChiCunGuanIds;
//    private String mzResultType;
//    private Integer mzId;
//
//
//    @ApiModelProperty(value = "顺序是：左手寸关尺，右手寸关尺")
//    private String pulsePotentials;
//    @ApiModelProperty(value = "脉位描述：顺序是：左手寸关尺，右手寸关尺")
//    private String maiWei;
//    private Double maiWeiMin = 200D;
//    private Double maiWeiMax = 350D;
//
//    @ApiModelProperty(value = "脉率描述：顺序是：左手寸关尺，右手寸关尺")
//    private String maiLv;
//
//    private Double maiLvMin = 60D;
//    private Double maiLvMax = 100D;
//
//    @ApiModelProperty(value = "节率描述：顺序是：左手寸关尺，右手寸关尺")
//    private String jieLv;
//
//    private Double jieLvMin = 0D;
//    private Double jieLvMax = 0.15D;
//
//    @ApiModelProperty(value = "脉力描述：顺序是：左手寸关尺，右手寸关尺")
//    private String maiLi;
//
//    private Double maiLiMin = 16D;
//    private Double maiLiMax = 25D;
//
//    @ApiModelProperty(value = "紧张度描述：顺序是：左手寸关尺，右手寸关尺")
//    private String jinZhangDu;
//
//
//    private Double jinZhangDuMax = 0.7D;
//
//    @ApiModelProperty(value = "流利度描述：顺序是：左手寸关尺，右手寸关尺")
//    private String liuLiDu;
//
//    private Double liuLiDuMax = 0.6D;
//
//
//    @ApiModelProperty(value = "整体描述")
//    private String totalPulseFeatureDesc;
//    private String pulseQuantificationImg;
//}
