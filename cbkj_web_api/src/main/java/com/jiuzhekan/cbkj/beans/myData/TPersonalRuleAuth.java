package com.jiuzhekan.cbkj.beans.myData;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TPersonalRuleAuth implements Serializable{

    @ApiModelProperty(value = "")
    private Long authId;

    @ApiModelProperty(value = "协定方角色ID")
    private String ruleId;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "科室是否全选")
    private Boolean deptCheckAll;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "协定方ID")
    private String persPreId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    public TPersonalRuleAuth(String ruleId, String deptId, Boolean deptCheckAll, String userId, String persPreId) {
        this.ruleId = ruleId;
        this.deptId = deptId;
        this.deptCheckAll = deptCheckAll;
        this.userId = userId;
        this.persPreId = persPreId;
    }
}
