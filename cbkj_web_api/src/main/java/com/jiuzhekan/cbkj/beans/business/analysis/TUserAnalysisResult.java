package com.jiuzhekan.cbkj.beans.business.analysis;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "用户体质辨识结果类", description = "用户体质辨识结果类")
public class TUserAnalysisResult implements Serializable {

    @ApiModelProperty(value = "辨识ID")
    private String analyId;

    @ApiModelProperty(value = "辨识编号")
    private String analyCode;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "挂号ID")
    private String registerId;

    @ApiModelProperty(value = "医生ID")
    private String doctorId;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "辨识时间")
    private Date analyTime;

    @ApiModelProperty(value = "主体质")
    private String subResult;

    @ApiModelProperty(value = "显示体质")
    private String displayResult;

    @ApiModelProperty(value = "兼体质")
    private String deputyResult;

    @ApiModelProperty(value = "倾向体质")
    private String biasedResult;

    @ApiModelProperty(value = "主体质ID")
    private String subGroupId;

    @ApiModelProperty(value = "医生建议")
    private String docOptions;

    @ApiModelProperty(value = "是否删除（0否  1是）")
    private String isDel;

    @ApiModelProperty(value = "Word地址")
    private String wordPath;

    @ApiModelProperty(value = "PDF地址")
    private String pdfPath;


    @ApiModelProperty(value = "分组描述")
    private String description;
    @ApiModelProperty(value = "调体法则 (注意知识库name是全小写,这里有大写)")
    private String ttRule;
    @ApiModelProperty(value = "调体要点 (注意知识库注意知识库name是全小写,这里有大写)")
    private String ttPoint;
    @ApiModelProperty(value = "调体方药 (注意知识库注意知识库name是全小写,这里有大写)")
    private String ttPrescription;
    @ApiModelProperty(value = "临证加减()")
    private String lzjj;
    @ApiModelProperty(value = "养生建议()")
    private String vegetables;
    @ApiModelProperty(value = "运动建议")
    private String sports;


    @ApiModelProperty(value = "情志调摄")
    private String emotionalAdjustment;
    @ApiModelProperty(value = "饮食调养")
    private String dietRecuperation;
    @ApiModelProperty(value = "起居调摄")
    private String dailyLifeAdjustment;
    @ApiModelProperty(value = "穴位保健")
    private String acupointHealthCare;
    @ApiModelProperty(value = "穴位保健穴位")
    private List<TUserAnalysisResultAcu> acupointList;
    @ApiModelProperty(value = "推荐药膳")
    private String medicatedDiet;
    @ApiModelProperty(value = "推荐花茶")
    private String flowerTea;
    @ApiModelProperty(value = "其他")
    private String other;
    @ApiModelProperty(value = "得分图片")
    private String scoreImg;
    @ApiModelProperty(value = "问卷类型 1标准2公卫")
    private Integer analyType;


    @TianAnDecryptField
    @ApiModelProperty(value = "患者姓名,首页输入框用这个接收参数(患者表)")
    private String patientName;
    @ApiModelProperty(value = "患者姓名拼音(患者表)")
    private String patientPy;
    @ApiModelProperty(value = "患者姓名五笔(患者表)")
    private String patientWb;
    @ApiModelProperty(value = "患者年龄(患者表)")
    private String patientAge;
    @TianAnDecryptField
    @ApiModelProperty(value = "患者性别(患者表)")
    private String patientGender;
    @ApiModelProperty(value = "患者生日(患者表)")
    private Date patientBirthday;
    @ApiModelProperty(value = "机构名称")
    private String insName;

    @ApiModelProperty(value = "查询条件-科室")
    private String deptId;
    @ApiModelProperty(value = "查询条件-医生姓名")
    private String doctorName;
    @ApiModelProperty(value = "查询条件-开始时间")
    private String beginTime;
    @ApiModelProperty(value = "查询条件-结束时间")
    private String endTime;

    private boolean canUpdate;
    private boolean canDelete;

    @ApiModelProperty(value = "各组得分信息( 例如:阳虚体质 50分)")
    private List<TUserAnalysisGroupResult> groupResultS;

    @ApiModelProperty(value = "问题勾选记录")
    private List<TUSerAnalysusResultItem> resultItemList;

    @ApiModelProperty(value = "结果体质集合（体质下拉框）")
    private String resultList;


}