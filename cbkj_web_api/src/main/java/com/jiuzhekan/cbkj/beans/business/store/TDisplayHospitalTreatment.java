package com.jiuzhekan.cbkj.beans.business.store;


import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel("药房配置院内治疗")
public class TDisplayHospitalTreatment implements Serializable {

    @ApiModelProperty("是否显示院内治疗（0显示，1不显示）")
    private String showHospitalTreatment;

    @ApiModelProperty("是否默认勾选院内治疗（0勾选，1不勾选）")
    private String isHospitalTreatment;

    @ApiModelProperty(value = "通用费用明细")
    private List<TDisplayMoneySetting> usuallyMoneySetting;

    public TDisplayHospitalTreatment(String price) {

        this.showHospitalTreatment = Constant.BASIC_STRING_ZERO;
        this.isHospitalTreatment = Constant.BASIC_STRING_ONE;

        this.usuallyMoneySetting = new ArrayList<>();
        TDisplayMoneySetting setting = new TDisplayMoneySetting("00000000", "00000000", "治疗费", new BigDecimal(StringUtils.isBlank(price) ? "0" : price));
        usuallyMoneySetting.add(setting);
    }
}
