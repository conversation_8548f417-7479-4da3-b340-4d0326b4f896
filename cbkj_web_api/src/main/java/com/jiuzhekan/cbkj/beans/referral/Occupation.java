package com.jiuzhekan.cbkj.beans.referral;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/24 10:55
 * @Version 1.0
 */
public enum Occupation {

    // 定义枚举常量，每个常量对应一个职业代码和职业名称
    STATE_CIVIL_SERVANT("11", "国家公务员"),
    PROFESSIONAL_TECHNICIAN("13", "专业技术人员"),
    CLERK("17", "职员"),
    ENTERPRISE_MANAGER("21", "企业管理人员"),
    WORKER("24", "工人"),
    FARMER("27", "农民"),
    STUDENT("31", "学生"),
    ACTIVE_MILITARY("37", "现役军人"),
    FREELANCER("51", "自由职业者"),
    SELF_EMPLOYED("54", "个体经营者"),
    UNEMPLOYED("70", "无业人员"),
    RETIRED("80", "退（离）休人员"),
    OTHER("90", "其他");

    // 定义私有字段来存储职业代码和职业名称
    private final String code;
    private final String name;

    // 枚举构造器，必须是私有的
    Occupation(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // 提供公共的getter方法来访问职业代码和职业名称
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    // 可以添加一个根据职业代码查找枚举的静态方法（如果需要的话）
    public static Occupation findByCode(String code) {
        for (Occupation occupation : Occupation.values()) {
            if (occupation.getCode().equals(code)) {
                return occupation;
            }
        }
        throw new IllegalArgumentException("No such occupation code: " + code);
    }

    // 如果需要，还可以添加其他静态方法或实例方法

    public static LinkedHashMap<String, String> getAllEthnicitiesAsMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        for (Occupation ethnicity : Occupation.values()) {
            map.put(ethnicity.getCode(), ethnicity.getName());
        }
        LinkedHashMap<String, String> sortedMap = map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(String::compareTo)) // 这里使用了默认的字符串比较
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, // 合并函数，这里其实不会用到，因为每个键都是唯一的
                        LinkedHashMap::new // 使用 LinkedHashMap 来保持排序后的顺序
                ));

        return sortedMap;

    }
}
