package com.jiuzhekan.cbkj.beans.sysBeans;

import com.jiuzhekan.cbkj.common.utils.Constant;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应实体
 */
@Data
public class ResEntity<T> implements Serializable {

    private static final long serialVersionUID = -1439573222637546375L;
    private boolean status = true;
    private int code = 0;
    private String message;
    private T data;



    private boolean isHasNextPage;

    private String count;

    public static final int SUCCESS_CODE = 200;
    public static final int FAIL_CODE = 500;
    public static final String SUCCESS_DX = "SUCCESS";
    public static final String FAIL_DX = "FAIL";

    public ResEntity() {

    }


    public ResEntity(boolean status, String message) {
        this.status = status;
        this.message = message;
    }

    public ResEntity(boolean status, T data) {
        this.status = status;
        this.data = data;
    }


    public ResEntity(boolean status, String message, T data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    public ResEntity(boolean status, int code, String message, T data) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static ResEntity entity(boolean status, String message, Object data) {
        return new ResEntity(status, message, data);
    }

    public static ResEntity success(Object data) {
        return new ResEntity(true, SUCCESS_CODE, Constant.SUCCESS_DX, data);
    }
    public static ResEntity success() {
        return new ResEntity(true, SUCCESS_CODE, Constant.SUCCESS_DX, null);
    }

    public static ResEntity error(String message) {
        return new ResEntity(false, FAIL_CODE, message, null);
    }
    public static ResEntity error(String message, Object data) {
        return new ResEntity(false, FAIL_CODE, message, data);
    }

    public static ResEntity error(int code, String message) {
        return new ResEntity(false, code, message, null);
    }

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Object getIsHasNextPage() {
        return isHasNextPage;
    }

    public void setIsHasNextPage(boolean hasNextPage) {
        isHasNextPage = hasNextPage;
    }

}

