package com.jiuzhekan.cbkj.beans.referral;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/24 10:46
 * @Version 1.0
 */
import lombok.Getter;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

public enum Ethnicity {

    HAN("01", "汉族"),
    MONGOLIAN("02", "蒙古族"),
    HUI("03", "回族"),
    TIBETAN("04", "藏族"),
    UIGHUR("05", "维吾尔族"),
    MIAO("06", "苗族"),
    YI("07", "彝族"),
    ZHUANG("08", "壮族"),
    BUYI("09", "布依族"),
    KOREAN("10", "朝鲜族"),
    MANCHU("11", "满族"),
    DONG("12", "侗族"),
    YAO("13", "瑶族"),
    BAI("14", "白族"),
    TUJIA("15", "土家族"),
    HANI("16", "哈尼族"),
    KAZAK("17", "哈萨克族"),
    DAI("18", "傣族"),
    LI("19", "黎族"),
    LISU("20", "傈僳族"),
    WA("21", "佤族"),
    SHE("22", "畲族"),
    GAOSHAN("23", "高山族"),
    LAHU("24", "拉祜族"),
    SHUI("25", "水族"),
    DONGXIANG("26", "东乡族"),
    NAXI("27", "纳西族"),
    JINGPO("28", "景颇族"),
    KIRGHIZ("29", "柯尔克孜族"),
    TU("30", "土族"),
    DAGUR("31", "达斡尔族"),
    MULAM("32", "仫佬族"),
    QIANG("33", "羌族"),
    BULANG("34", "布朗族"),
    SALAR("35", "撒拉族"),
    MAONAN("36", "毛难族"), // 注意：现官方称为毛南族
    GELAO("37", "仡佬族"),
    XIBE("38", "锡伯族"),
    ACHANG("39", "阿昌族"),
    PUMI("40", "普米族"),
    TAJIK("41", "塔吉克族"),
    NU("42", "怒族"),
    UZBEK("43", "乌孜别克族"),
    RUSSIAN("44", "俄罗斯族"),
    EWENKI("45", "鄂温克族"),
    DEANG("46", "德昂族"),
    BAOAN("47", "保安族"),
    YUGUR("48", "裕固族"),
    JING("49", "京族"),
    TATAR("50", "塔塔尔族"),
    DULONG("51", "独龙族"),
    OROQEN("52", "鄂伦春族"),
    HEZHE("53", "赫哲族"),
    MENBA("54", "门巴族"),
    LUOBA("55", "珞巴族"),
    JINO("56", "基诺族");

    @Getter
    private final String code;
    @Getter
    private String name;



    Ethnicity(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // 可以添加一个根据code查找枚举的静态方法
    public static Ethnicity findByCode(String code) {
        for (Ethnicity ethnicity : Ethnicity.values()) {
            if (ethnicity.getCode().equals(code)) {
                return ethnicity;
            }
        }
        throw new IllegalArgumentException("No such ethnicity code: " + code);
    }
    // 返回所有民族代码到民族名称的映射（LinkedHashMap保持插入顺序）
    public static LinkedHashMap<String, String> getAllEthnicitiesAsMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        for (Ethnicity ethnicity : Ethnicity.values()) {
            map.put(ethnicity.getCode(), ethnicity.getName());
        }


        LinkedHashMap<String, String> sortedMap = map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(  String::compareTo )) // 根据值排序
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, // 合并函数，这里其实不会用到，因为每个键都是唯一的
                        LinkedHashMap::new // 使用 LinkedHashMap 来保持排序后的顺序
                ));

        return sortedMap;
    }

}
