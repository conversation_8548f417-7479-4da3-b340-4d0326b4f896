package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TPersonalPrescriptionDisMapping implements Serializable{

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "")
    private String persPreId;

    @ApiModelProperty(value = "")
    private String disId;

    @ApiModelProperty(value = "")
    private String disName;

    @ApiModelProperty(value = "")
    private String symId;

    @ApiModelProperty(value = "")
    private String symName;

    @ApiModelProperty(value = "")
    private String theCode;

    @ApiModelProperty(value = "")
    private String theNames;


}
