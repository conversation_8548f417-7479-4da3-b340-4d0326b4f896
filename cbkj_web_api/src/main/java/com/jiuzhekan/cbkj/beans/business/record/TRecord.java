package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import com.jiuzhekan.cbkj.common.utils.PublicVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TRecord extends PublicVo implements Serializable {

    @ApiModelProperty("病历ID")
    private String recId;

    @ApiModelProperty(value = "挂号ID", required = true)
    private String registerId;

    @ApiModelProperty(value = "患者ID", required = true)
    private String patientId;

    @ApiModelProperty("患者姓名")
    private String recName;

    @ApiModelProperty("患者年龄1")
    private Short recAge1;

    @ApiModelProperty("患者年龄单位1")
    private String recAgeunit1;

    @ApiModelProperty("患者年龄2")
    private Short recAge2;

    @ApiModelProperty("患者年龄单位2")
    private String recAgeunit2;

    @ApiModelProperty("患者性别")
    private String recGender;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty("患者住址")
    private String recAddress;

    @ApiModelProperty("是否怀孕")
    private String gravidity;

    @ApiModelProperty("就诊时间")
    private Date recTreTime;

    @ApiModelProperty("医生ID")
    private String docId;

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医疗机构代码")
    private String insCode;

    @ApiModelProperty("就诊科室ID")
    private String deptId;

    @ApiModelProperty("就诊科室名称")
    private String deptName;

    @ApiModelProperty("就诊科室名称")
    private String hospitalNo;

    @ApiModelProperty("就诊科室名称")
    private String wardId;

    @ApiModelProperty("就诊科室名称")
    private String wardName;

    @ApiModelProperty("就诊科室名称")
    private String bedNo;

    @ApiModelProperty("医疗机构就诊记录ID")
    private String insTreId;

    @ApiModelProperty("病历类型（1门诊、2住院医嘱）")
    private String recTreType;

    @ApiModelProperty("西医诊断ID")
    private String westernDiseaseId;

    @ApiModelProperty("西医诊断结果")
    private String westernDisease;

    @ApiModelProperty(value = "疾病ID", required = true)
    private String disId;

    @ApiModelProperty(value = "疾病名称", required = true)
    private String disName;

    @ApiModelProperty(value = "证型ID", required = true)
    private String symId;

    @ApiModelProperty(value = "证型名称", required = true)
    private String symName;

    @ApiModelProperty(value = "治法编码")
    private String theCode;

    @ApiModelProperty(value = "治法字符串", required = true)
    private String theNames;

    @ApiModelProperty("主诉")
    private String patientContent;

    @ApiModelProperty("现病史")
    private String nowDesc;

    @ApiModelProperty("既往史")
    private String pastDesc;

    @ApiModelProperty("家族史")
    private String familyDesc;

    @ApiModelProperty("过敏史")
    private String allergyDesc;

    @ApiModelProperty("舌像")
    private String tongue;

    @ApiModelProperty("脉象")
    private String pulse;

    @ApiModelProperty("中医四诊")
    private String fourDiagnosis;

    @ApiModelProperty("体格检查")
    private String physical;

    @ApiModelProperty("辅助检查")
    private String auxiliaryExam;

    @ApiModelProperty("治疗意见")
    private String treatmentAdvice;

    @ApiModelProperty("个人史")
    private String personalDesc;

    @ApiModelProperty("初诊病历ID")
    private String recFirstid;

    @ApiModelProperty("诊次")
    private Integer visNum;

    @ApiModelProperty("末次服药日期")
    private Date recLastdate;

    @ApiModelProperty("在线复诊（0不支持在线复诊 3：三天内 7：七天内 15：15天内在线复诊 90：3个月内在线复诊）")
    private Integer recOnlineRediagnosis;

    @ApiModelProperty("在线复诊有效时间")
    private Date recRediagnosisDeadline;
    @ApiModelProperty("审核类型（0为自动审核，1为人工审核）")
    private String checkType;
    @ApiModelProperty("审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty("审核不通过原因")
    private String checkAdvise;

    private String isDel;


    //以下为关联表字段
    private String recDiagnosisStatus;  //病例状态

    private String time;                //就诊时间（1 上午 2 下午）

    private String dateStr;

    private String dateStr2;

    @ApiModelProperty("医联体名称")
    private String appName;

    @ApiModelProperty("医疗机构名称")
    private String insName;

    @ApiModelProperty(hidden = true, value = "上级机构id,没有就为空")
    private String insPcode;

    private String medicalCardNo;   //就诊卡号

    private String decoctType;      //煎药方式

    private String orderNumber;         //单号

    private String isReceive;       //收货标志

    @TianAnDecryptField
    private String mobile;          //联系方式

    @TianAnDecryptField
    private String certificate;     //身份证号

    @TianAnDecryptField
    private String address;         //地址

    private Date birthday;        //出生日期

    @ApiModelProperty("医生名称")
    private String docName;

    private String complaint;       //主诉

    private String content;         //内容

    @ApiModelProperty("HIS病历号")
    private String visitNo;

    @ApiModelProperty("病历从表集合")
    private List<TRecordDetail> recordDetailList;

    @ApiModelProperty(value = "处方表", required = true)
    private List<TPrescription> prescriptionList;

    private TPatients patients;
    @ApiModelProperty(value = "身高")
    private String patientHeight;

    @ApiModelProperty(value = "体重")
    private String patientWeight;
    @ApiModelProperty(value = "当前挂号ID", required = true)
    private String currentRegisterId;
    private String version;
    private String doctorName;

    /**
     * 以下参数-只能作为搜索时条件使用，且不能作为返回值。其他不可使用
     */
//    @TianAnEncryptField
//    private String patientName;
    @TianAnEncryptField
    private String patientMobile;

    @TianAnEncryptField
    private String patientCertificate;
}