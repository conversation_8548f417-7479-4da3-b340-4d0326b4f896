package com.jiuzhekan.cbkj.beans.business.zkxcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcSz implements Serializable{

    @ApiModelProperty(value = "")
    private Integer szId;

    @ApiModelProperty(value = "其他特征")
    private String crackStr;

    @ApiModelProperty(value = "")
    private String fnStandard;

    @ApiModelProperty(value = "腐腻指数")
    private double fnIndex;

    @ApiModelProperty(value = "腐腻特征")
    private String fnStr;

    @ApiModelProperty(value = "")
    private String hbStandard;

    @ApiModelProperty(value = "厚薄指数")
    private double hbIndex;

    @ApiModelProperty(value = "厚薄特征，")
    private String hbStr;

    @ApiModelProperty(value = "苔色")
    private String lCoatStr;

    @ApiModelProperty(value = "")
    private String lnStandard;

    @ApiModelProperty(value = "舌嫩指数")
    private double lnIndex;

    @ApiModelProperty(value = "")
    private String lnStr;

    @ApiModelProperty(value = "舌尖")
    private String lZhiStr;

    @ApiModelProperty(value = "胖瘦指数")
    private double psIndex;

    @ApiModelProperty(value = "")
    private String psStandard;

    @ApiModelProperty(value = "舌体胖瘦")
    private String psStr;

    @ApiModelProperty(value = "")
    private String tCoatStandard;

    @ApiModelProperty(value = "苔舌指数")
    private double tCoatIndex;

    @ApiModelProperty(value = "舌象分割图")
    private String tongueDivisionImg;

    @ApiModelProperty(value = "舌象原图")
    private String tongueOriginalImg;

    @ApiModelProperty(value = "")
    private String tZhiStandard;

    @ApiModelProperty(value = "舌色指数")
    private double tZhiIndex;

    @ApiModelProperty(value = "")
    private Integer collectId;

    @ApiModelProperty(value = "")
    private String registerId;




    @ApiModelProperty(value = "舌色标定")
    private String lZhiCalibration;

    @ApiModelProperty(value = "舌尖标定")
    private String lJianCalibration;

    @ApiModelProperty(value = "舌形(胖瘦)标定")
    private String psCalibration;

    @ApiModelProperty(value = "苔色标定")
    private String lCoatCalibration;

    @ApiModelProperty(value = "厚薄标定")
    private String hbCalibration;

    @ApiModelProperty(value = "腐腻标定")
    private String fnCalibration;



}
