package com.jiuzhekan.cbkj.beans.business.record.preCheckVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "处方审核保存请求类")
public class PreSaveReqVO implements Serializable {
    @ApiModelProperty(value = "处方id")
    private String preId;
    @ApiModelProperty(value = "处方编号")
    private String preNo;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "审核不通过原因")
    private String checkAdvise;
    private String preOrigin;

    private String checkUserid;
    private String checkUsername;
}