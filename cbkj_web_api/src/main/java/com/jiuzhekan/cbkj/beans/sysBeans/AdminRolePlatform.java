package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 角色
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel("角色")
public class AdminRolePlatform implements Serializable {

    private static final long serialVersionUID = -6335654944450560361L;
    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "角色英文名")
    private String roleName;

    @ApiModelProperty(value = "角色中文名")
    private String rnameZh;

    @ApiModelProperty(value = "备注说明")
    private String roleDesc;

    /**
     * 扩展第三方主键【】
     */
    private String objId;

    @ApiModelProperty(value = "首页地址")
    private String indexUrl;


}