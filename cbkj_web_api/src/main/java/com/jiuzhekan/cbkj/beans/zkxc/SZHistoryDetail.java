package com.jiuzhekan.cbkj.beans.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 11:45
 * @Version 1.0
 */
@ApiModel(value = "舌诊历史详情数据")
@Data
public class SZHistoryDetail {
    @ApiModelProperty(value = "舌色标定")
    private String lZhiCalibration;

    @ApiModelProperty(value = "舌色标定尖头 -1 向下 0 等于 1 向上")
    private int lZhiDownUp;



    @ApiModelProperty(value = "舌尖标定")
    private String lJianCalibration;

    @ApiModelProperty(value = "舌尖标定 -1 向下 0 等于 1 向上")
    private String lJianDownUp;

    @ApiModelProperty(value = "舌形(胖瘦)标定")
    private String psCalibration;
    @ApiModelProperty(value = "舌形(胖瘦)标定 -1 向下 0 等于 1 向上")
    private int psDownUp;

    @ApiModelProperty(value = "苔色标定")
    private String lCoatCalibration;

    @ApiModelProperty(value = "苔色标定 -1 向下 0 等于 1 向上")
    private int lCoatDownUp;

    @ApiModelProperty(value = "厚薄标定")
    private String hbCalibration;
    @ApiModelProperty(value = "厚薄标定 -1 向下 0 等于 1 向上")
    private int hbDownUp;

    @ApiModelProperty(value = "腐腻标定")
    private String fnCalibration;
    @ApiModelProperty(value = "腐腻标定 -1 向下 0 等于 1 向上")
    private int fnDownUp;

    @ApiModelProperty(value = "数据时间")
    private Date insertTime;
    private Integer collectId;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "舌原图")
    private String tongueOriginalImg;

    @ApiModelProperty(value = "舌色")
    private String lZhiStr;

    private Double lZhiMin = 0D;
    private Double lZhiMax = 1D;

    @ApiModelProperty(value = "舌尖")
    private String lJianStr;

    private Double lJianMin = 0D;

    private Double lJianMax = 1D;

    @ApiModelProperty(value = "舌形(胖瘦)特征")
    private String psStr;

    private Double psMin = 0.39D;

    private Double psMax = 1D;

    @ApiModelProperty(value = "舌形标准")
    private String psStandard = "0.39~1";
    @ApiModelProperty(value = "舌形(胖瘦)指数")
    private Double psIndex;

    @ApiModelProperty(value = "舌色指数")
    private Double tZhiIndex;
    private Double tZhiMax = 1d;
    private Double tZhiMin = 0d;
    private String tZhiStandard;

    @ApiModelProperty(value = "舌色特征")
    private Double tZhiStr;

    @ApiModelProperty(value = "苔色特征")
    private String lCoatStr;

    @ApiModelProperty(value = "苔色指数")
    private Double lCoatIndex;
    private void setlZhiDownUp() {
        if (this.tZhiIndex < 0 ){
            this.lZhiDownUp = -1;
        }else if (this.tZhiIndex > 0 ){
            this.lZhiDownUp = 1;
        }else {
            this.lZhiDownUp = 0;
        }
    }
    private void setlCoatDownUp() {
        if (this.lCoatIndex < 0 ){
            this.lCoatDownUp = -1;
        }else if (this.lCoatIndex > 0 ){
            this.lCoatDownUp = 1;
        }else {
            this.lCoatDownUp = 0;
        }
    }

    private void setpsDownUp() {
        if (this.psIndex < 0.39 ){
            this.psDownUp = -1;
        }else if (this.psIndex > 1 ){
            this.psDownUp = 1;
        }else {
            this.psDownUp = 0;
        }
    }

    private void setHbDownUp() {
        if (this.hbIndex < 0.22 ){
            this.hbDownUp = -1;
        }else if (this.hbIndex > 1 ){
            this.hbDownUp =1;
        }else {
            this.hbDownUp = 0;
        }
    }
    private void setFnDownUp() {
        if (this.fnIndex < 0 ){
            this.fnDownUp = -1;
        }else if (this.fnIndex > 1 ){
            this.fnDownUp = 1;
        }else {
            this.fnDownUp = 0;
        }
    }

    private void setLnDownUp() {
        if (this.lnIndex < 0 ){
            this.lnDwonUp =-1;
        }else if (this.lnIndex > 1 ){
            this.lnDwonUp = 1;
        }else {
            this.lnDwonUp = 0;
        }
    }

    public void initDownUp(){
        setLnDownUp();
        setFnDownUp();
        setHbDownUp();
        setpsDownUp();
        setlCoatDownUp();
        setlZhiDownUp();
    }



    private Double lCoatMin = 0D;
    private Double lCoatMax = 1D;

    @ApiModelProperty(value = "苔色标准")
    private String lCoatStandard = "0~1";



    @ApiModelProperty(value = "厚薄特征")
    private String hbStr;

    private Double hbMin = 0.22D;

    private Double hbMax = 1D;

    @ApiModelProperty(value = "厚薄标准")
    private String hbStandard = "少苔（<0.22），薄（0.22~1）";
    @ApiModelProperty(value = "厚薄指数")
    private Double hbIndex;

    @ApiModelProperty(value = "腐腻特征")
    private String fnStr;

    private Double fnMin = 0D;

    private Double fnMax = 1D;
    @ApiModelProperty(value = "腐腻标准")
    private String fnStandard = "0~1";
    @ApiModelProperty(value = "腐腻指数")
    private Double fnIndex;

    @ApiModelProperty(value = "舌像整体结果")
    private String tTotalStr;

    @ApiModelProperty(value = "舌嫩特征")
    private String lnStr;

    @ApiModelProperty(value = "舌嫩指数")
    private Double lnIndex;
    private int lnDwonUp;

    @ApiModelProperty(value = "舌嫩指数标准")
    private String lnStandard = "0.82~1";

    private Double lnMin = 0.82D;
    private Double lnMax = 1D;


}
