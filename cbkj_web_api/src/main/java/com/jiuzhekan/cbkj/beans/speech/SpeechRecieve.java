package com.jiuzhekan.cbkj.beans.speech;

import org.codehaus.jackson.annotate.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SpeechRecieve {
    private List<String> zhuSu;
    private List<String> xianBinShi;
    private List<String> jiWangShi;
    private List<String> tiGeJianCha;
    private List<String> fuZhuJianCha;
    private List<String> zhiLiaoYiJian;
    private String height;
    private String weight;
    private List<Symptoms> symptoms;

    private String name;
    private String id;
    private String group;

    public void setName(String name) {
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getId() {
        return id;
    }

    public void setGroup(String group) {
        this.group = group;
    }
    public String getGroup() {
        return group;
    }

    public void setHeight(String height) {
        this.height = height;
    }
    public String getHeight() {
        return height;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }
    public String getWeight() {
        return weight;
    }

    public void setSymptoms(List<Symptoms> symptoms) {
        this.symptoms = symptoms;
    }
    public List<Symptoms> getSymptoms() {
        return symptoms;
    }


    public List<String> getZhuSu() {
        return zhuSu;
    }

    public void setZhuSu(List<String> zhuSu) {
        this.zhuSu = zhuSu;
    }

    public List<String> getXianBinShi() {
        return xianBinShi;
    }

    public void setXianBinShi(List<String> xianBinShi) {
        this.xianBinShi = xianBinShi;
    }

    public List<String> getJiWangShi() {
        return jiWangShi;
    }

    public void setJiWangShi(List<String> jiWangShi) {
        this.jiWangShi = jiWangShi;
    }

    public List<String> getTiGeJianCha() {
        return tiGeJianCha;
    }

    public void setTiGeJianCha(List<String> tiGeJianCha) {
        this.tiGeJianCha = tiGeJianCha;
    }

    public List<String> getFuZhuJianCha() {
        return fuZhuJianCha;
    }

    public void setZhiLiaoYiJian(List<String> zhiLiaoYiJian) {
        this.zhiLiaoYiJian = zhiLiaoYiJian;
    }

    public List<String> getZhiLiaoYiJian() {
        return zhiLiaoYiJian;
    }

    public void setFuZhuJianCha(List<String> fuZhuJianCha) {
        this.fuZhuJianCha = fuZhuJianCha;
    }
}
