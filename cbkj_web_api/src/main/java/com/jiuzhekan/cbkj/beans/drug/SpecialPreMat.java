package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * SpecialPreMat
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/5/25 11:40
 */

@Data
@ApiModel
@NoArgsConstructor
public class SpecialPreMat extends CenterHisMappingVO {

    @ApiModelProperty(value = "能否使用")
    private Boolean canUsage;
    @ApiModelProperty(value = "不能使用提示")
    private String cannotUsageTip;

    @ApiModelProperty(value = "开始时间")
    private String hasStartDate;
    @ApiModelProperty(value = "结束时间")
    private String hasEndDate;

    @ApiModelProperty(value = "合并药品集合")
    private List<SpecialPreMat> matList;

    public SpecialPreMat(CenterHisMappingVO centerHisMappingVO) {
        BeanUtils.copyProperties(centerHisMappingVO, this);
    }
}
