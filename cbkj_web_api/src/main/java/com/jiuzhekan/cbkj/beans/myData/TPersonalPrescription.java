package com.jiuzhekan.cbkj.beans.myData;

import com.jiuzhekan.cbkj.beans.business.prescription.TPersonalPrescriptionDisMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("协定方类")
public class TPersonalPrescription implements Serializable {
    @ApiModelProperty(value = "行数", example = "10")
    private Integer limit = 10;
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;
    @ApiModelProperty(value = "协定方ID")
    private String persPreId;
    @ApiModelProperty(value = "")
    private String appId;
    @ApiModelProperty(value = "")
    private String insCode;
    @ApiModelProperty(value = "科室ID")
    private String deptId;
    @ApiModelProperty(value = "方名(请求的时候后台判断是中英文)")
    private String preName;
    @ApiModelProperty(value = "协定方类型（1为内服中药，2为外用中药，3为膏方）")
    private String preType;
    @ApiModelProperty(value = "拥有者")
    private String preOwner;
    @ApiModelProperty(value = "分享医生")
    private String preOwnerName;
    @ApiModelProperty(value = "功效与适应症")
    private String efficacy;
    @ApiModelProperty(value = "方解")
    private String annotation;
    @ApiModelProperty(value = "拼音")
    private String prePy;
    @ApiModelProperty(value = "五笔")
    private String preWb;
    @ApiModelProperty(value = "共享标记（0私有 1本科室 2本医疗机构 3医联体-院内方）默认0")
    private String isShare;

    @ApiModelProperty(value = "是否启用 1是 0否")
    private String isUse;

    @ApiModelProperty(value = "是否科研处方（1是0否）")
    private Integer isScientificPreparation;

    @ApiModelProperty(value = "科研编号 ")
    private String infoScientificPreparation;

    private Date insertDate;
    private Date updateDate;
    private Date delDate;
    private String isDel;

    @ApiModelProperty(value = "开方时是否能修改（0否  1是）")
    private String isModify;

    @ApiModelProperty(value = "协定方来源（1 HIS）")
    private Short preOrigin;

    @ApiModelProperty(value = "贴数（草药为贴数，熏蒸为总次数，适宜技术为天数）")
    private Short preNum;

    @ApiModelProperty(value = "服法ID")
    private String preDescriptionId;

    @ApiModelProperty(value = "服法")
    private String preDescription;

    @ApiModelProperty(value = "频次ID")
    private String preFrequencyId;

    @ApiModelProperty(value = "频次")
    private String preFrequency;

    @ApiModelProperty(value = "外用方式ID")
    private String preSmokeTypeId;

    @ApiModelProperty(value = "外用方式")
    private String preSmokeType;

    @ApiModelProperty(value = "药品剂型（1散装饮片  2散装颗粒  4小包装饮片 5小包装颗粒  6膏方）")
    private String preMatType;

    @ApiModelProperty(value = "药房ID")
    private String storeId;
    @ApiModelProperty(value = "门诊/住院标识(1门诊 2住院)")
    private String preMzZy;
    @ApiModelProperty(value = "序号")
    private Integer preOrder;
    @ApiModelProperty(value = "是否配方（1是0否）")
    private Integer isPreparation;
    @ApiModelProperty(value = "能否加药")
    private Integer canAppend;


    /**
     * 院内方字段
     */
    @ApiModelProperty(value = "疾病ID || 开方界面的协定方，如果医生有疾病，传这个字段")
    private String disId;
    @ApiModelProperty(value = "疾病名称")
    private String disName;
    @ApiModelProperty(value = "证型ID || 开方界面的协定方，如果医生有证型，传这个字段")
    private String symId;
    @ApiModelProperty(value = "治法代码 || 开方界面的协定方，如果医生有治法，传这个字段")
    private String theCode;
    private String theName;
    @ApiModelProperty(value = "证型名称")
    private String symName;
    //    @ApiModelProperty(value = "治法字符串")
//    private String theNames;
    @ApiModelProperty(value = "专家")
    private String preExpert;
    @ApiModelProperty(value = "专家名衔（1国医大师 2国家级名老中医 3省级名老中医 4名医）")
    private String preExpertTitle;


    @ApiModelProperty(value = "机构名称")
    private String insName;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "查询类别  0我的协定方 1科室协定方 2机构协定方 3院内方 多个逗号拼接")
    private String shareType;

    @ApiModelProperty(value = "协定方详情")
    private List<TPersonalPrescriptionItem> perItems;

    @ApiModelProperty(value = "协定方详情的药品名称用  空格 拼接起来")
    private String preItem;

    @ApiModelProperty(value = "我的协定方维护界面能否修改 1能 0否")
    private String canUpdate;

    @ApiModelProperty(value = "我的协定方维护界面能否删除 1能 0否")
    private String canDelete;

    @ApiModelProperty(value = "挂号ID，用于区分当前是门诊还是住院")
    private String registerId;


    @ApiModelProperty(value = "是否未分类")
    private boolean isOther;

    @ApiModelProperty(value = "文件夹ID")
    private String folderId;

    @ApiModelProperty(value = "文件夹ID集合")
    private List<String> folderIdList;
    private List<FolderIdD> folders;
    @ApiModelProperty(value = "搜索个人协定方是否过滤医疗机构  0（不过滤，可以使用全部个人协定方）1（过滤，只能使用当前医疗机构的个人协定方）")
    private boolean filterSelfByIns;
    @ApiModelProperty(value = "搜索科室协定方是否过滤当前科室  0否（可以看所有授权的科室协定方） 1是（只能看当前科室的科室协定方）")
    private boolean filterDept;
    @ApiModelProperty(value = "搜索全院协定方是否过滤当前机构  0否（可以看所有授权的全院协定方） 1是（只能看当前机构的全院协定方）")
    private boolean filterInsCode;
    @ApiModelProperty(value = "搜索医共体协定方是否过滤当前医共体  0否（可以看所有授权的医共体协定方） 1是（只能看当前医共体的所有协定方）")
    private boolean filterApp;

    @ApiModelProperty(value = "HIS-协定方序号")
    private String xh;

    @ApiModelProperty(value = "HIS-协定方名称")
    private String xhName;
    @ApiModelProperty(value = "新增协定方的关联病症法")
    private List<TPersonalPrescriptionDisMapping> tPersonalPrescriptionDisMappingsList;
}
