package com.jiuzhekan.cbkj.beans.drug;

import com.jiuzhekan.cbkj.beans.interaction.DrugInventory;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

@Data
public class TCenterYpkc implements Serializable{

    private String id;

    private String storeId;

    private String storeName;

    private String ypmlId;

    private String yaopindm;

    private BigDecimal kucunsl;

    private String chandiid;

    private String chandimc;

    private BigDecimal jinjia;

    private BigDecimal lingshoujia;

    private String yaopindmTy;

    private String zifu1;

    private String zifu2;

    private String zifu3;

    private String zifu4;

    private String zifu5;

    public TCenterYpkc() {
    }

    public TCenterYpkc(DrugInventory drug,String ypdmTy) {
        this.id = IDUtil.getID();
        this.storeId = drug.getStoreId();
        this.storeName = drug.getStoreName();
        this.ypmlId = drug.getYpmlId();
        this.yaopindm = drug.getYaoPinDm();
        this.kucunsl = drug.getKuCunSl();
        this.chandiid = drug.getChanDiId();
        this.chandimc = drug.getChanDiMc();
        this.jinjia = drug.getLingShouJia();
        this.lingshoujia = drug.getLingShouJia();
        this.yaopindmTy = ypdmTy;
    }
}