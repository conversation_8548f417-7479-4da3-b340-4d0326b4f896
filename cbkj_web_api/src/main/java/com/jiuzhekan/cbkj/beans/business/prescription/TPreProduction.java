package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel("处方制膏")
public class TPreProduction implements Serializable{

    @ApiModelProperty(value = "处方ID")
    private String preId;

    @ApiModelProperty(value = "膏方类型ID")
    private String typeId;

    @ApiModelProperty(value = "膏方类型名称")
    private String typeName;

    @ApiModelProperty(value = "包装方式ID")
    private String dicId;

    @ApiModelProperty(value = "包装方式代码")
    private String dicCode;

    @ApiModelProperty(value = "包装方式名称")
    private String dicName;

    @ApiModelProperty(value = "包装方式收费代码")
    private String chargeCode;

    @ApiModelProperty(value = "包装方式收费项目名称")
    private String chargeName;

    @ApiModelProperty(value = "价格")
    private Double price;


}
