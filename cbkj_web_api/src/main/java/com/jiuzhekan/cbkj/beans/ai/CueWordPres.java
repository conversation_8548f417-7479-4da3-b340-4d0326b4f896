package com.jiuzhekan.cbkj.beans.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/6 10:08
 * @Version 1.0
 */
@Data
@ApiModel
public class CueWordPres {
    @ApiModelProperty(value = "0空白方 1智能辩证 2智能推方 3方剂搜索 4协定方转方 5院内方转方 6我的验案转方 7名家验案转方 8历史病历转方 9国医大师 10配方 11传承经方 12膏方")
    private String preOrigin;
    @ApiModelProperty(value = "贴数")
    private String preNum;
    @ApiModelProperty(value = "内用服法/外用方式")
    private String preDescription;

    @ApiModelProperty(value = "饭前饭后服")
    private String preUsetimeDes;

    @ApiModelProperty(value = "频次")
    private String preFrequency;

    @ApiModelProperty(value = "每次/每袋多少ml")
    private String preNMlName;
    @ApiModelProperty(value = "熏蒸仪器名称")
    private String preSmokeInstrument;
    @ApiModelProperty(value = "适宜技术-类型")
    private String acuType;
    @ApiModelProperty(value = "适宜技术-针刺种类")
    private String acuProject;

    @ApiModelProperty(value = "处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）")
    private String preType;

    private List<CueWordURLMatRequest> matList;
}
