package com.jiuzhekan.cbkj.beans.formula;

import com.jiuzhekan.cbkj.common.utils.Constant;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
public class TFormulaHttpToFormulaVo {

    public static TFormula transferFormulaBean(TFormulaHttp vo1, TFormula vo2) {
        if (null == vo1) {
            return null;
        }
        vo2.setFormulaId(StringUtils.isBlank(vo1.getAgrId()) ? null : vo1.getAgrId());
        vo2.setPreName(vo1.getPreName());
        vo2.setEfficacy(vo1.getEffect());
        vo2.setPreType(vo1.getCategory());
        vo2.setPreItem(vo1.getPreItem());
        vo2.setPreStock(StringUtils.isBlank(vo1.getPreStock()) ? new BigDecimal("0") : new BigDecimal(vo1.getPreStock()));
        vo2.setIsModify(Constant.BASIC_STRING_ZERO);
        vo2.setCanAppend(Constant.BASIC_STRING_ZERO);
        //vo2.setIsMedical(vo1.getIsMedical());
        vo2.setPayType(vo1.getPayType());
        ArrayList<TFormulaItem> items = new ArrayList<>();
        if (null != vo1.getItemList()) {
            List<TFormulaItemHttp> itemList = vo1.getItemList();
            for (int i = 0; i < itemList.size(); i++) {
                TFormulaItemHttp item = itemList.get(i);
                if (null != item) {
                    TFormulaItem tFormulaItem = new TFormulaItem();
                    tFormulaItem.setFormulaId(vo2.getFormulaId());
                    tFormulaItem.setYpmlHis(item.getDrugIdHis());
                    tFormulaItem.setYpggdmHis(item.getMatSpeIdHis());
                    tFormulaItem.setYpmlCenter(item.getDrugId());
                    tFormulaItem.setYpggdmCenter(item.getMatSpeId());
                    tFormulaItem.setMatId(item.getKMatId());
                    tFormulaItem.setYpdmCenter(item.getMatPriceId());
                    tFormulaItem.setYpdmHis(item.getMatPriceIdHis());
                    tFormulaItem.setYpmcCenter(item.getMatName());
                    tFormulaItem.setYpmcHis(item.getMatNameHis());
                    tFormulaItem.setBzdwHis(item.getMatUnitHis());
                    tFormulaItem.setYaopindmTy(tFormulaItem.getYpdmHis());
                    tFormulaItem.setMatDose(item.getMatDoseHis());
                    tFormulaItem.setYfidHis(item.getUsageCodeHis());
                    //tFormulaItem.setYfmcHis(item.getUsageDescHis());
                    tFormulaItem.setMatXsj(StringUtils.isBlank(item.getSmallRetailPriceHis()) ? null : new BigDecimal(item.getSmallRetailPriceHis()));
                    //tFormulaItem.setKucunsl(item.getStockNum());
                    tFormulaItem.setCenterKucunsl(item.getStockNum());
                    tFormulaItem.setZhuanhuanxs(StringUtils.isBlank(item.getConversionFactorHis()) ? null : Short.parseShort(item.getConversionFactorHis()));
                    tFormulaItem.setMatXsj(StringUtils.isBlank(item.getSmallRetailPriceHis()) ? null : new BigDecimal(item.getSmallRetailPriceHis()));
                    tFormulaItem.setMatId(item.getKMatId());
                    tFormulaItem.setCenterYpgg(item.getMatSpeName());
                    tFormulaItem.setHisYpgg(item.getMatSpeNameHis());
                    tFormulaItem.setHisGgid(item.getMatSpeIdHis());
                    tFormulaItem.setCenterGgid(item.getMatSpeId());
                    tFormulaItem.setCdidCenter(item.getMatOriginId());
                    tFormulaItem.setCenterYpcd(item.getMatOriginName());
                    tFormulaItem.setCenterStoreId(item.getPhaId());
                    tFormulaItem.setCenterStoreId(item.getPhaId());
                    tFormulaItem.setCenterYplx(item.getMatType());
                    tFormulaItem.setHisYplx(StringUtils.isBlank(item.getMatTypeHis()) ? null : Integer.parseInt(item.getMatTypeHis()));
                    tFormulaItem.setCdmcCenter(item.getMatOriginName());
                    tFormulaItem.setUsage(item.getMatUsageHis());
                    tFormulaItem.setUsageCode(item.getUsageCodeHis());
                    items.add(tFormulaItem);
                }

            }

        }
        vo2.setItemList(items);
        return vo2;
    }
}
