package com.jiuzhekan.cbkj.beans.formula;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TFormulaItem implements Serializable{

    @ApiModelProperty(value = "配方明细ID")
    private String itemId;

    @ApiModelProperty(value = "配方ID")
    private String formulaId;

    @NotEmpty(message = "HIS药品目录代码不能为空")
    @ApiModelProperty(value = "HIS药品目录代码")
    private String ypmlHis;

    @NotEmpty(message = "HIS药品规格代码不能为空")
    @ApiModelProperty(value = "HIS药品规格代码")
    private String ypggdmHis;

    @NotEmpty(message = "中心药品目录代码不能为空")
    @ApiModelProperty(value = "中心药品目录代码")
    private String ypmlCenter;

    @NotEmpty(message = "中心药品规格代码不能为空")
    @ApiModelProperty(value = "中心药品规格代码")
    private String ypggdmCenter;

    @ApiModelProperty(value = "知识库药品ID")
    private String matId;

    @ApiModelProperty(value = "统一药品代码（由开方系统生成）")
    private String yaopindmTy;

    @ApiModelProperty(value = "药品代码（存药房的）")
    private String ypdmCenter;

    @NotEmpty(message = "药品代码（存HIS的）不能为空")
    @ApiModelProperty(value = "药品代码（存HIS的）")
    private String ypdmHis;

    @NotEmpty(message = "药品名称（存药房的）不能为空")
    @ApiModelProperty(value = "药品名称（存药房的）")
    private String ypmcCenter;

    @NotEmpty(message = "药品名称（存HIS的）不能为空")
    @ApiModelProperty(value = "药品名称（存HIS的）")
    private String ypmcHis;

    @NotEmpty(message = "药品规格（存HIS的）不能为空")
    @ApiModelProperty(value = "药品规格（存HIS的）")
    private String ypggHis;


    @NotEmpty(message = "包装单位（存HIS的）不能为空")
    @ApiModelProperty(value = "包装单位（存HIS的）")
    private String bzdwHis;

    @NotNull(message = "剂量不能为空")
    @ApiModelProperty(value = "剂量")
    private BigDecimal matDose;

    @ApiModelProperty(value = "用法ID（存HIS的）")
    private String yfidHis;

    @ApiModelProperty(value = "用法名称（存HIS的）")
    private String yfmcHis;

    @ApiModelProperty(value = "单价")
    private BigDecimal matXsj;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal kucunsl;


    @ApiModelProperty(value = "序号")
    private Integer matSeqn;

    @NotNull(message = "转换系数不能为空")
    @ApiModelProperty(value = "转换系数")
    private Short zhuanhuanxs;

    @ApiModelProperty(value = "新增时间")
    private Date createDate;

    @ApiModelProperty(value = "新增人")
    private String createUser;

    private BigDecimal centerKucunsl;
    private String centerYpgg;
    private String hisYpgg;
    private String hisGgid;
    private String centerGgid;
    private String cdidCenter;
    private String centerYpcd;
    private String centerStoreId;
    private String centerYplx;
    private String cdmcCenter;
    private Integer hisYplx;
    private String usage;
    private String usageCode;


}
