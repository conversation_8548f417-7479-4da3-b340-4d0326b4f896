package com.jiuzhekan.cbkj.beans.zkxc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcEquimentPush implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "zkxc_equipment_patient表主键id")
    private Integer equipmentPatientId;

    @ApiModelProperty(value = "设备mac地址")
    private String macAddress;

    @ApiModelProperty(value = "失败原因")
    private String failInfo;

    @ApiModelProperty(value = "1.未推送 2.已推送  10.推送失败")
    private Integer pushStatus;

    @ApiModelProperty(value = "挂号id")
    private String registerId;

    @ApiModelProperty(value = "类型：1.推给his2.推给云系统3.推给脉象仪")
    private Integer type;

    @ApiModelProperty(value = "推送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

}
