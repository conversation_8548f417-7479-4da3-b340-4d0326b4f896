package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.beans.business.record.TRecordDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "临时用来接收数据")
@Data
public class TRecordPreTempVO implements Serializable {

    private String recId;

    private String patientContent;

    private String symName;

    private String disName;
    private String recName;          // 患者姓名
    private String recTreTime;       // 就诊时间
    private String recLastdate;     // 末次服药日期

    private String preId;

    private String preNo;           // 处方号

    private String preType;         // 处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方）
    private String preMatType;
    private String storeId;
    private String visNum;          // 诊次
    private String recFirstid;      // 初诊uuid
    private String refund;          // 是否退费,有值就是退了,没值就是没退
}