package com.jiuzhekan.cbkj.beans.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 11:45
 * @Version 1.0
 */
@ApiModel
@Data
public class FaceHistory {

    private Integer collectId;
    private Date insertTime;

    private String faceOriginalImg;

    @ApiModelProperty(value = "面色")
    private String color;

    @ApiModelProperty(value = "光泽")
    private String light;

    @ApiModelProperty(value = "唇色")
    private String lip;



}
