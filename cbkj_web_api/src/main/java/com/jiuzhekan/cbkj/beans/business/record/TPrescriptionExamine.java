package com.jiuzhekan.cbkj.beans.business.record;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel("处方审核")
public class TPrescriptionExamine implements Serializable{

    @ApiModelProperty(value = "处方审核ID")
    private String preExaId;

    @ApiModelProperty(value = "处方ID")
    private String preId;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "审核类型（0为自动审核，1为人工审核）")
    private String checkType;

    @ApiModelProperty(value = "审核状态（1审核通过 2审核未通过）")
    private String isCheck;

    @ApiModelProperty(value = "审核意见")
    private String checkAdvise;

    @ApiModelProperty(value = "审核人ID")
    private String checkUserid;

    @ApiModelProperty(value = "审核人姓名")
    private String checkUsername;

    @ApiModelProperty(value = "审核时间")
    private Date checkTime;

    @ApiModelProperty(value = "审核时间")
    private String checkTimeStr;

    @ApiModelProperty(value = "")
    private Date delDate;

    @ApiModelProperty(value = "")
    private String isDel;


}
