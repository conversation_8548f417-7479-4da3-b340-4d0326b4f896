package com.jiuzhekan.cbkj.beans.business.store;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
public class HTTPDisPlay implements Serializable {
    @ApiModelProperty(value = "显示ID")
    private String id;

    @NotEmpty(message = "显示名称不能为空")
    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    private String phaId;
    private String phaName;
    private String sort;
    private String isDefault;
    private Date createDate;
    private String createUser;
    /**
     * 中药类型（1散装饮片 2散装颗粒 3膏方 4小包装饮片 5小包装颗粒 6配方 7制剂 8 中成药
     */
    private String matType;
}
