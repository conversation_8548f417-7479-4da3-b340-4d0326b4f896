package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class THisXdf implements Serializable{

    @ApiModelProperty(value = "HIS-协定方序号")
    private String xh;

    @ApiModelProperty(value = "HIS-协定方名称")
    private String name;

    @ApiModelProperty(value = "使用范围 0全院 1科室 2 个人")
    private String syfw;


}
