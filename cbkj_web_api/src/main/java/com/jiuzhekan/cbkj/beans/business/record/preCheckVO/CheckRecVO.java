package com.jiuzhekan.cbkj.beans.business.record.preCheckVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * CheckPreVO
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/9/8
 */
@Data
@ApiModel(value = "审核病历")
public class CheckRecVO implements Serializable {
    private String recId;
    @ApiModelProperty(value = "医共体代码")
    private String appId;
    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;
    @ApiModelProperty(value = "医疗机构")
    private String insName;
    @ApiModelProperty(value = "医生姓名")
    private String doctorName;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "疾病")
    private String disName;
    @ApiModelProperty(value = "证型")
    private String symName;
    @ApiModelProperty(value = "治法")
    private String theNames;
    @ApiModelProperty(value = "申请时间")
    private String recTime;

    @ApiModelProperty(value = "开始时间('2019-10-01 00:00:00')")
    private String startDate;
    @ApiModelProperty(value = "结束时间")
    private String endDate;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "审核类型（0为自动审核，1为人工审核，2下班自动通过）")
    private String checkType;

    @ApiModelProperty(value = "包含下级")
    private Boolean lower = false;

    @ApiModelProperty(value = "审核处方列表")
    private List<CheckPreVO> checkPreList;
}
