package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 药品查询vo层
 */
@Data
public class MatVo implements Serializable {
    /**
     * 新华需求：药品剂量，一个药需要有多规格：5.21.9 ： http://121.41.90.252:8080/zentao/story-view-2771.html
     */
    private List<String> matDoseList;
    //his药品目录ID
    private String drugIdHis;

    //his药品价格id
    private String matPriceIdHis;

    //his药品id
    private String matIdHis;

    //his药品名称
    private String matNameHis;

    //his规格id
    private String matSpeIdHis;

    //his规格
    private String matSpeNameHis;

    //his中药类型
    private String matTypeHis;

    //his颗粒转成饮片的转换系数
    private String conversionFactorHis;

    //his剂量
    private BigDecimal matDoseHis;

    //his剂量单位
    private String matDoseUnitHis;

    //是否医保
    private String isMedicalHis;

    //用法代码
    private String usageCodeHis;

    //用法代码
    private String matUsageHis;

    //用法用量
    private String usageDescHis;

    //小规格零售价
    private String smallRetailPriceHis;

    //批准文号
    private String approvalNumberHis;

    //日最大剂量（内服）
    private String dailyMaxDoseInHis;

    //日最大剂量（外用）
    private String dailyMaxDoseExtHis;

    //医保日最大开药量（制剂）
    private String dailyMaxNumPrepHis;

    //单独使用时不予支付
    private String notPayAloneHis;

    //不纳入基金支付范围
    private String notPayInFundHis;

    //频次ID（制剂）
    private String frequencyIdHis;

    //频次（制剂）
    private String frequencyHis;

    //频次系数(次数/天)（制剂）
    private String frequencyRateHis;



    //药房药品目录ID
    private String drugId;

    //药房药品价格id
    private String matPriceId;

    //药品id
    private String matId;

    //药房药品名称
    private String matName;

    //规格id
    private String matSpeId;

    //规格
    private String matSpeName;

    //药品产地id
    private String matOriginId;

    //产地名称
    private String matOriginName;

    //中药类型
    private String matType;

    //药房ID
    private String phaId;

    //颗粒转成饮片的转换系数
    private String conversionFactor;

    //剂量
    private BigDecimal matDose;

    //剂量单位
    private String matDoseUnit;
    //his药品单位
    private String matUnitHis;
    //药品单位
    private String matUnit;

    //小规格零售价
    private String smallRetailPrice;

    //库存数量
    private BigDecimal stockNum;



    //知识库药品ID
    private String kMatId;

    public String getkMatName() {
        return kMatName;
    }

    public void setkMatName(String kMatName) {
        this.kMatName = kMatName;
    }

    //知识库药品名称
    private String kMatName;

    //知识库药品用法
    private String kMatYf;

    //是否有其他产地(是否合并产地)
    private Boolean hasMore = false;

    //是否有其他产地的提示
    private String hasMoreTip;

    private Integer speCount;//规格数量

    private String oldMatPriceIdHis;//原his药品id
    private String effect;//功能主治
    private String content;//内容


    private String matOnceDose;//中药制剂-一次剂量
    private String matOnceDoseUnit;//中药制剂-一次计量单位
    //提纯系数
    private String purificationFactor;
    // 医保说明
    private String xzsm;

    private String toxicityOverdoseMultiple; //毒药倍数
    private String externalUseOnly; //仅限外用(0关1开)

   // @ApiModelProperty(value = "最大剂量")
    private Double maxdose;

   // @ApiModelProperty(value = "最大剂量")s
    private Double mindose;

    public String getkMatId() {
        return kMatId;
    }

    public void setkMatId(String kMatId) {
        this.kMatId = kMatId;
    }
}
