package com.jiuzhekan.cbkj.beans.business.syndrome;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/14 09:48
 * @Version 1.0
 */
@ApiModel
@Data
public class TRecordSyndromeTonguePulseAnswer {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "分组记录ID")
    private String rsGroupId;

    @ApiModelProperty(value = "问题ID")
    private String code;

    @ApiModelProperty(value = "问题名称")
    private String name;

    @ApiModelProperty(value = "1舌2脉")
    private Integer type;
}
