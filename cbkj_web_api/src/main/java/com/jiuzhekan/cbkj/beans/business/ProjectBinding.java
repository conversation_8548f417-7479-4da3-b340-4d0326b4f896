package com.jiuzhekan.cbkj.beans.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class ProjectBinding implements Serializable{

    @ApiModelProperty(value = "主键id")
    private Integer bid;

    @ApiModelProperty(value = "收费编码")
    private String payCode;

    @ApiModelProperty(value = "项目名称")
    private String proName;

    @ApiModelProperty(value = "项目内涵")
    private String proConnotation;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "计价单位")
    private String unitOfAccount;

    @ApiModelProperty(value = "分类名称")
    private String sortName;

    @ApiModelProperty(value = "分类编码")
    private String sortCode;

    @ApiModelProperty(value = "项目拼音")
    private String proPy;

    @ApiModelProperty(value = "项目五笔")
    private String proWb;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "备注信息")
    private String memo;

    @ApiModelProperty(value = "状态【0:可用，1:禁用】")
    private String status;



    @ApiModelProperty(value = "医共体Id")
    private String appId;

    @ApiModelProperty(value = "医疗机构Id")
    private String insCode;

    @ApiModelProperty(value = "科室Id")
    private String deptId;



}
