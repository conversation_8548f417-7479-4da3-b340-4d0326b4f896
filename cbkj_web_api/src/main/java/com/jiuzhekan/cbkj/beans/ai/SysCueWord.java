package com.jiuzhekan.cbkj.beans.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysCueWord implements Serializable{

    @ApiModelProperty(value = "")
    private String cueWordId;

    @ApiModelProperty(value = "1.AI方解2.诊断依据")
    private String cueWordType;

    private String preOrigin;

    @ApiModelProperty(value = "提示词文本内容")
    private String cueWordText;

    @ApiModelProperty(value = "转化词语")
    private String cueTransText;

    @ApiModelProperty(value = "提示词文本内容-展示用")
    private String displayText;
    private String cueWordAgentId;

    @ApiModelProperty(value = "医共体appid")
    private String appId;

    @ApiModelProperty(value = "机构代码")
    private String insCode;

    @ApiModelProperty(value = "0正常1失效")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "删除人id")
    private String delUser;

    @ApiModelProperty(value = "删除人id")
    private String delUserName;

    @ApiModelProperty(value = "删除时间")
    private Date delTime;


}
