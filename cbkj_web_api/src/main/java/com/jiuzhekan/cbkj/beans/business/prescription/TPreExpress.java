package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel("处方配送")
public class TPreExpress implements Serializable{

    @ApiModelProperty(value = "处方ID")
    private String preId;

    @ApiModelProperty(value = "取药方式ID")
    private String dicId;

    @ApiModelProperty(value = "取药方式代码")
    private String dicCode;

    @ApiModelProperty(value = "取药方式名称")
    private String dicName;

    @ApiModelProperty(value = "取药方式收费代码")
    private String chargeCode;

    @ApiModelProperty(value = "取药方式收费项目名称")
    private String chargeName;

    @ApiModelProperty(value = "价格")
    private Double price;


}
