package com.jiuzhekan.cbkj.beans.myData;

import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
public class TPersonalPrescriptionFolder implements Serializable {

    @ApiModelProperty(value = "协定方文件夹ID")
    private String folderId;

    @ApiModelProperty(value = "文件夹名称")
    private String folderName;

    @ApiModelProperty(value = "文件夹序号")
    private Integer folderNum;

    @ApiModelProperty(value = "共享级别（0私有 1科室 2全院）")
    private String folderType;

    @ApiModelProperty(value = "父文件ID")
    private String folderPid;

    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUsername;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "修改人姓名")
    private String updateUsername;

    @ApiModelProperty(value = "删除时间")
    private Date delDate;

    @ApiModelProperty(value = "删除人")
    private String delUser;

    @ApiModelProperty(value = "删除人姓名")
    private String delUsername;

    @ApiModelProperty(value = "是否末级")
    private boolean isLeaf = true;

    @ApiModelProperty(value = "能否修改、能否移动")
    private boolean canUpdate;

    @ApiModelProperty(value = "能否新增下级")
    private boolean canAddChildren;

    @ApiModelProperty(value = "子文件夹")
    private List<TPersonalPrescriptionFolder> children;

    @ApiModelProperty(value = "文件夹排序")
    private List<TPersonalPrescriptionFolder> folderSort;



    public TPersonalPrescriptionFolder selfFolder() {
        this.setFolderId(Constant.FOLDER_SELF);
        this.setFolderName("个人");
        this.setFolderType(Constant.BASIC_STRING_ZERO);
        this.setLeaf(false);
        this.setChildren(new ArrayList<>());
        TPersonalPrescriptionFolder other = new TPersonalPrescriptionFolder();
        other.setFolderId(Constant.FOLDER_SELF_OTHER);
        other.setFolderPid(Constant.FOLDER_SELF);
        other.setFolderName("未分类");
        this.setFolderType(Constant.BASIC_STRING_ZERO);
        this.getChildren().add(other);
        return this;
    }

    public TPersonalPrescriptionFolder deptFolder() {
        this.setFolderId(Constant.FOLDER_DEPT);
        this.setFolderName("科室");
        this.setFolderType(Constant.BASIC_STRING_ONE);
        this.setLeaf(false);
        this.setChildren(new ArrayList<>());
        TPersonalPrescriptionFolder other = new TPersonalPrescriptionFolder();
        other.setFolderId(Constant.FOLDER_DEPT_OTHER);
        other.setFolderPid(Constant.FOLDER_DEPT);
        other.setFolderName("未分类");
        this.setFolderType(Constant.BASIC_STRING_ONE);
        this.getChildren().add(other);
        return this;
    }

    public TPersonalPrescriptionFolder insFolder() {
        this.setFolderId(Constant.FOLDER_INS);
        this.setFolderName("全院");
        this.setFolderType(Constant.BASIC_STRING_TWO);
        this.setLeaf(false);
        this.setChildren(new ArrayList<>());
        TPersonalPrescriptionFolder other = new TPersonalPrescriptionFolder();
        other.setFolderId(Constant.FOLDER_INS_OTHER);
        other.setFolderPid(Constant.FOLDER_INS);
        other.setFolderName("未分类");
        this.setFolderType(Constant.BASIC_STRING_TWO);
        this.getChildren().add(other);
        return this;
    }

    public TPersonalPrescriptionFolder appFolder() {
        this.setFolderId(Constant.FOLDER_APP);
        this.setFolderName("医共体");
        this.setFolderType(Constant.BASIC_STRING_FOUR);
        this.setLeaf(false);
        this.setChildren(new ArrayList<>());
        TPersonalPrescriptionFolder other = new TPersonalPrescriptionFolder();
        other.setFolderId(Constant.FOLDER_APP_OTHER);
        other.setFolderPid(Constant.FOLDER_APP);
        other.setFolderName("未分类");
        this.setFolderType(Constant.BASIC_STRING_FOUR);
        this.getChildren().add(other);
        return this;
    }

    public TPersonalPrescriptionFolder areaFolder() {
        this.setFolderId(Constant.FOLDER_AREA);
        this.setFolderName("区域");
        this.setFolderType(Constant.BASIC_STRING_FIVE);
        this.setLeaf(false);
        this.setChildren(new ArrayList<>());
        TPersonalPrescriptionFolder other = new TPersonalPrescriptionFolder();
        other.setFolderId(Constant.FOLDER_AREA_OTHER);
        other.setFolderPid(Constant.FOLDER_AREA);
        other.setFolderName("未分类");
        this.setFolderType(Constant.BASIC_STRING_FIVE);
        this.getChildren().add(other);
        return this;
    }

    public TPersonalPrescriptionFolder knowFolder() {
        this.setFolderId(Constant.FOLDER_KNOW);
        this.setFolderName("方剂");
        this.setLeaf(false);
        this.setChildren(new ArrayList<>());
        return this;
    }

    public TPersonalPrescriptionFolder formulaFolder() {
        this.setFolderId(Constant.FOLDER_FORMULA);
        this.setFolderName("配方");
        this.setLeaf(true);
        this.setChildren(new ArrayList<>());
        return this;
    }

}
