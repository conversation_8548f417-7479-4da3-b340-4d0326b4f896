package com.jiuzhekan.cbkj.beans.http;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "专病列表",description = "国医大师的专病列表数据")
public class ZbDisease implements Serializable {

    @ApiModelProperty(value = "疾病id")
    private String diseaseId;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "别名")
    private String aliasName;
}