package com.jiuzhekan.cbkj.beans.business.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: pre_api
 * @description: 国医大师结果表
 * @author: wangtao
 * @create: 2021-03-25 10:08
 **/
@Data
public class TRecordMaster implements Serializable {
    private String registerId;
    private String diseaseCode;
    private String disId;
    private String patientContent;
    private String nowDesc;
    private String pastDesc;
    private String fourDiagnosis;
    private String physical;
    private String auxiliaryExam;
    private String jsonStr;
    private Date saveTime;
    private String originType;
    private String originId;
    private String originUrl;

    @ApiModelProperty(value = " 智能辨证推导跳转:智能开方  中医电子病历  特色电子病历")
    private String url;



}
