package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.beans.business.patients.TDcAddress;
import com.jiuzhekan.cbkj.beans.business.prescription.TPreDecoction;
import com.jiuzhekan.cbkj.beans.business.prescription.TPreExpress;
import com.jiuzhekan.cbkj.beans.business.prescription.TPreProduction;
import com.jiuzhekan.cbkj.beans.business.store.TDisplayHospitalTreatment;
import com.jiuzhekan.cbkj.beans.dosageform.TPrescriptionPreparation;
import com.jiuzhekan.cbkj.beans.validRecordBySDK.EvaluationResponse;
import com.jiuzhekan.cbkj.common.constant.OrderStatusConstant;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class TPrescription implements Serializable {

    @ApiModelProperty(value = "处方ID")
    private String preId;

    @ApiModelProperty(" 院内治疗信息")
    private TDisplayHospitalTreatment hospitalTreatment;

    @ApiModelProperty(value = "处方号码")
    private String preNo;

    private String displayId;
    @ApiModelProperty(value = "处方保存接口校验库存开关1是0否")
    private String preStockSwitch;
    @ApiModelProperty(value = "是否预扣计算0否1是")
    private String withholdSwitch;

    @ApiModelProperty(value = "病历ID")
    private String recId;

    @ApiModelProperty(value = "病患姓名")
    private String patientName;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "开方医生ID")
    private String preDoctor;

    @ApiModelProperty(value = "开方医生姓名")
    private String preDoctorname;

    @ApiModelProperty(value = "开方时间")
    private Date preTime;

    @ApiModelProperty(value = "单剂金额", required = true)
    private BigDecimal preSingleMoney;

    @ApiModelProperty(value = "贴数", required = true)
    private Short preNum;

    @ApiModelProperty(value = "门诊住院标志（1门诊 2住院）")
    private Short preMzZy;

    @ApiModelProperty(value = "处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）", required = true)
    private String preType;

    @ApiModelProperty(value = "药品剂型（1散装饮片  2散装颗粒 3膏方  4小包装饮片 5小包装颗粒 7制剂）", required = true)
    private String preMatType;

    @ApiModelProperty(value = "用法", required = true)
    private String preUsage;

    @ApiModelProperty(value = "服法ID", required = true)
    private String preDescriptionId;

    @ApiModelProperty(value = "服法", required = true)
    private String preDescription;

    @ApiModelProperty(value = "频次ID", required = true)
    private String preFrequencyId;

    @ApiModelProperty(value = "频次", required = true)
    private String preFrequency;

    @ApiModelProperty(value = "服药时间ID")
    private String preUsetimeId;

    @ApiModelProperty(value = "服药时间说明")
    private String preUsetimeDes;

    @ApiModelProperty(value = "医嘱", required = true)
    private String preAdvice;

    @ApiModelProperty(value = "处方来源(0空白方 1智能辩证 2智能推方 3方剂搜索 4协定方转方 5院内方转方 6我的验案转方 7名家验案转方 8历史病历转方 9国医大师 10配方 11传承经方 12膏方 13安全合理用药sdk过来的处方)")
    private String preOrigin;

    @ApiModelProperty(value = "来源处方ID")
    private String preOriginId;

    @ApiModelProperty(value = "HIS来源处方号序号")
    private String preOriginXh;

    @ApiModelProperty(value = "来源处方名")
    private String preOriginName;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private String isDel;

    @ApiModelProperty(value = "删除人ID")
    private String delUserid;

    @ApiModelProperty(value = "删除人姓名")
    private String delUsername;

    @ApiModelProperty(value = "审核类型（0自动审核，1人工审核，2下班自动通过）")
    private String checkType;

    //recExtType=10，isCheck=NULL为草稿中；recExtType=10，isCheck=0为待审核
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;

    @ApiModelProperty(value = "审核不通过原因")
    private String checkAdvise;

    @ApiModelProperty(value = "审核人ID")
    private String checkUserid;

    @ApiModelProperty(value = "审核人姓名")
    private String checkUsername;

    @ApiModelProperty(value = "审核时间")
    private Date checkTime;

    @ApiModelProperty(value = "收退费状态（0未收费 1已收费 2已退费）")
    private String isPay;

    @ApiModelProperty(value = "收退费时间")
    private Date payTime;

    @ApiModelProperty(value = "收退费人ID")
    private String payUserid;

    @ApiModelProperty(value = "收退费人姓名")
    private String payUsername;

    @ApiModelProperty(value = "打印次数")
    private Short isPrint;

    @ApiModelProperty(value = "药品总金额", required = true)
    private BigDecimal matTolMoney;

    @ApiModelProperty(value = "处方总金额(药品总金额加代煎等费用)", required = true)
    private BigDecimal preTolMoney;

    @ApiModelProperty(value = "处方实收总金额")
    private BigDecimal preTolMoneyReal;

    @ApiModelProperty(value = "特殊调配费")
    private BigDecimal preSpecialFee;

    @ApiModelProperty(value = "HIS处方ID")
    private String hisPreId;

    /*****代煎*****/
    @ApiModelProperty(value = "处方代煎信息")
    private TPreDecoction decoction;
    @ApiModelProperty(value = "煎药方式（1为代煎，0为自煎）", required = true)
    private String decoctType;
    @ApiModelProperty(value = "代煎贴数")
    private Short preDecoctNum;
    @ApiModelProperty(value = "代煎费", required = true)
    private BigDecimal preDecoctionFee;

    /*****制膏*****/
    @ApiModelProperty(value = "处方制膏信息")
    private TPreProduction production;
    @ApiModelProperty(value = "是否膏方（0否 1是）", required = true)
    private String isProduction;
    @ApiModelProperty(value = "膏方类型代码")
    private String productionTypeId;
    @ApiModelProperty(value = "膏方类型")
    private String productionType;
    @ApiModelProperty(value = "制膏费", required = true)
    private BigDecimal preProductionFee;

    /*****配送*****/
    @ApiModelProperty(value = "处方配送信息")
    private TPreExpress express;
    @ApiModelProperty(value = "取药方式（1配送，0自提）", required = true)
    private String dcType;
    @ApiModelProperty(value = "配送费", required = true)
    private BigDecimal preExpressFee;


    //recExtType=10，isCheck=NULL为草稿中；recExtType=10，isCheck=0为待审核
    /**
     * 参阅 {@link OrderStatusConstant}
     */
    @ApiModelProperty(value = "处方状态（3预约挂号 5挂号缴费 7咨询缴费 10开方 20删除 30审核通过 40审核未通过 44推送成功 45推送失败 50处方缴费 61药房审核通过 62药房审核未通过 80配药 82复核 85泡药 90发药 100取消发药 110退药 120取消退药 130煎药 135包装 140配送 150收货）")
    private String recExtType;

    @ApiModelProperty(value = "药房ID", required = true)
    private String storeId;
    private String preLabel;

    @ApiModelProperty(value = "药房名称", required = true)
    private String storeName;

    @ApiModelProperty(value = "配送ID")
    private String dcId;

    @ApiModelProperty(value = "配送类型（0.定点配送点1.配送到家）")
    private String dcTypeSubclass;

    private String selfPickupId;

    @ApiModelProperty(value = "适宜技术类型ID", required = true)
    private String acuTypeId;

    @ApiModelProperty(value = "适宜技术类型", required = true)
    private String acuType;

    @ApiModelProperty(value = "针灸项目ID", required = true)
    private String acuProjectId;

    @ApiModelProperty(value = "针灸项目", required = true)
    private String acuProject;

    @ApiModelProperty(value = "操作指南", required = true)
    private String acuOperation;

    @ApiModelProperty(value = "外用方式ID")
    private String preSmokeTypeId;

    @ApiModelProperty(value = "外用方式")
    private String preSmokeType;

    @ApiModelProperty(value = "治疗费", required = true)
    private BigDecimal preSmoMoney;

    @ApiModelProperty(value = "熏蒸仪ID")
    private String preSmokeInstrumentId;

    @ApiModelProperty(value = "熏蒸仪")
    private String preSmokeInstrument;

    @ApiModelProperty(value = "每次几袋")
    private String preNBag;

    @ApiModelProperty(value = "每次几ml")
    private String preNMl;

    @ApiModelProperty(value = "每次几ml")
    private String preNMlName;

    @ApiModelProperty(value = "原处方ID（退费处方要填原处方ID）")
    private String originPreId;

    @ApiModelProperty(value = "兼证加减ID", required = true)
    private String preSchemeaddid;

    @ApiModelProperty(value = "是否成功推送到药房(1是 0否)")
    private String isSend;

    @ApiModelProperty(value = "是否院内治疗（1是 0否）")
    private String isHospitalTreatment;

    @ApiModelProperty(value = "是否医保（0自付 1医保）")
    private String isInsurance;

    /**
     * 2020-10-13安吉增加特病
     */
    @ApiModelProperty("特病标志（0否 1是）")
    private String isSpecialDis;
    @ApiModelProperty("特病名称")
    private String specialName;
    @ApiModelProperty("特病代码")
    private String specialCode;

    @ApiModelProperty("患者类型(0自费1普通医保2医保离休3职工特慢J居民特慢)")
    private String patientTypes;

    private String patientTypesName;

    @ApiModelProperty(value = "修改时旧处方号")
    private String preOldNo;

    @ApiModelProperty(value = "住院医嘱类型（1临嘱，2长嘱）")
    private String preAdviceType;

    @ApiModelProperty(value = "医嘱时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String preAdviceTime;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "开始时间")
    private String beganTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;
    private String drugId;
    private BigDecimal matTolWeight;

    @ApiModelProperty(value = "出院带药(1是，0否)")
    private String dischargeMedicationMark;

    @ApiModelProperty(value = "收费项目编码【001,002】逗号分隔")
    private String payCode;
    /******************EXT*****************/
    @ApiModelProperty(value = "处方明细集合（保存必填）", required = true)
    private List<TPrescriptionItem> itemList;

    @ApiModelProperty(value = "适宜技术处方明细表（保存适宜技术必填）", required = true)
    private List<TPrescriptionAcuItem> acuItemList;

    @ApiModelProperty(value = "医保支付条件限制")
    private List<String> insuranceEvaluation;

    @ApiModelProperty(value = "制剂医保限制")
    private List<String> preparationInsuranceEvaluation;

    @ApiModelProperty(value = "安全用药测评（保存必填）", required = true)
    private TPrescriptionEvaluation preSafetyEvaluation;
    private EvaluationResponse preSafetyEvaluationSDK;

    @ApiModelProperty(value = "经方相似度测评", required = true)
    private TPrescriptionEvaluation preSimilarityEvaluation;

    @ApiModelProperty(value = "处方配送表（保存必填）", required = true)
    private TDcAddress preDc;
    

    @ApiModelProperty(value = "是否科研处方（1是0否）")
    private Integer isScientificPreparation;

    @ApiModelProperty(value = "科研编号 ")
    private String infoScientificPreparation;
    @ApiModelProperty(value = "处方状态表")
    private List<TOrderStatus> statusList;

    @ApiModelProperty(value = "处方安全警告")
    private List preSafetyWarning;

    @ApiModelProperty(value = "中药制剂明细集合（保存必填）", required = true)
    private List<TPrescriptionPreparationItem> preparationItemList;


    @ApiModelProperty(value = "制剂信息-V6.1.0新增对象.没开制剂情况下，请传null")
    private TPrescriptionPreparation preparation;

    @ApiModelProperty(value = "是否加急0 不加急 1加急-V6.1.0新增")
    private Integer urgentSignValue;
    /**
     * 系统审核通过
     *
     * <AUTHOR>
     * @date 2020/9/8
     */
    public void sysCheckPass(String checkUser) {
        this.setCheckType(Constant.BASIC_STRING_ZERO);
        this.setIsCheck(Constant.BASIC_STRING_ONE);
        this.setCheckUsername(checkUser);
        this.setCheckTime(new Date());
        this.setRecExtType(OrderStatusConstant.CHECK_PASS.toString());
    }

    /**
     * 下班自动通过
     *
     * <AUTHOR>
     * @date 2020/9/8
     */
    public void workTimeOutPass(String checkUser) {
        this.setCheckType(Constant.BASIC_STRING_TWO);
        this.setIsCheck(Constant.BASIC_STRING_ONE);
        this.setCheckUsername(checkUser);
        this.setCheckTime(new Date());
        this.setRecExtType(OrderStatusConstant.CHECK_PASS.toString());
    }

    /**
     * 系统审核不通过
     *
     * <AUTHOR>
     * @date 2020/9/8
     */
    public void sysCheckFail() {
        this.setCheckType(Constant.BASIC_STRING_ONE);
        this.setIsCheck(Constant.BASIC_STRING_ZERO);
    }


    /**
     * 计算处方总金额（必须先算出药品总金额）
     */
    public void computePreTolMoney() {
        BigDecimal preTolMoney = this.getMatTolMoney();

        if (this.getPreDecoctionFee() != null) {
            preTolMoney = preTolMoney.add(this.getPreDecoctionFee());
        }
        if (this.getPreProductionFee() != null) {
            preTolMoney = preTolMoney.add(this.getPreProductionFee());
        }
        if (this.getPreExpressFee() != null) {
            preTolMoney = preTolMoney.add(this.getPreExpressFee());
        }
        if (this.getPreSpecialFee() != null) {
            preTolMoney = preTolMoney.add(this.getPreSpecialFee());
        }
        if (this.getPreSmoMoney() != null) {
            preTolMoney = preTolMoney.add(this.getPreSmoMoney());
        }
        this.setPreTolMoney(preTolMoney);
    }


}