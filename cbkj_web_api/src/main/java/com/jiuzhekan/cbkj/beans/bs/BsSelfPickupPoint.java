package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class BsSelfPickupPoint implements Serializable{

    @ApiModelProperty(value = "")
    private String selfPickupId;

    @ApiModelProperty(value = "自提点名称")
    private String selfPickupName;

    @ApiModelProperty(value = "自提点详细地址")
    private String selfPickupAddress;

    @ApiModelProperty(value = "自提点负责人")
    private String headName;

    @ApiModelProperty(value = "自提点电话")
    private String headPhone;

    @ApiModelProperty(value = "自提点类型名")
    private String selfPickupTypeName;

    @ApiModelProperty(value = "自提点类型代码01  中医药医院 请对照字典数据")
    private Integer selfPickupTypeCode;

    @ApiModelProperty(value = "省代码")
    private String dcProvinceCode;

    @ApiModelProperty(value = "省")
    private String dcProvince;

    @ApiModelProperty(value = "市代码")
    private String dcCityCode;

    @ApiModelProperty(value = "市")
    private String dcCity;

    @ApiModelProperty(value = "区代码")
    private String dcAreaCode;

    @ApiModelProperty(value = "区")
    private String dcArea;

    @ApiModelProperty(value = "街道代码")
    private String dcStreetCode;

    @ApiModelProperty(value = "街道")
    private String dcStreet;

    @ApiModelProperty(value = "村代码")
    private String dcVillageCode;

    @ApiModelProperty(value = "村")
    private String dcVillage;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "创建人名")
    private String createUsername;

    @ApiModelProperty(value = "0否 1是")
    private Integer isDel;

    @ApiModelProperty(value = "排序")
    private Integer sort;


}
