package com.jiuzhekan.cbkj.beans.drug;

import com.jiuzhekan.cbkj.beans.business.store.HTTPDisPlay;
import com.jiuzhekan.cbkj.beans.business.store.TDisplay;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

public class TransCenterHisMappingVoToMatVo {
    public static CenterHisMappingVO transferMatBean(MatVo matVo,CenterHisMappingVO vo1,String source) {
        vo1.setHisYpdm(matVo.getMatPriceIdHis()) ;
        vo1.setHisYpmlId(matVo.getDrugIdHis());
        vo1.setHisYpmc(matVo.getMatNameHis());
        vo1.setHisGgid(matVo.getMatSpeIdHis());
        vo1.setHisYpgg(matVo.getMatSpeNameHis());
        vo1.setHisYplx(StringUtils.isBlank(matVo.getMatTypeHis()) ? null : Integer.parseInt(matVo.getMatTypeHis()));
        vo1.setMatDoseList(matVo.getMatDoseList());
        vo1.setIsInsurance(matVo.getIsMedicalHis());
        if (StringUtils.isNotBlank(source)){
            if ("his".equals(source)){
                vo1.setUsageCode(matVo.getUsageCodeHis());
                vo1.setUsage(matVo.getMatUsageHis());
            }
            else if ("know".equals(source)){
                vo1.setUsage( matVo.getKMatYf());
            }else {
                vo1.setUsageCode("");
                vo1.setUsage("");
            }
        }



        vo1.setCenterYpcd(matVo.getMatOriginName());
        vo1.setApprovalNumber(matVo.getApprovalNumberHis());
        vo1.setDailyMaxDoseIn(matVo.getDailyMaxDoseInHis());
        vo1.setDailyMaxDoseExt(matVo.getDailyMaxDoseExtHis());
        vo1.setDailyMaxNumPrep(StringUtils.isBlank(matVo.getDailyMaxNumPrepHis()) ? null : Double.parseDouble(matVo.getDailyMaxNumPrepHis()));
        vo1.setFrequencyId(matVo.getFrequencyIdHis());
        vo1.setFrequency(matVo.getFrequencyHis());
        vo1.setFrequencyRate(matVo.getFrequencyRateHis());
        vo1.setCenterYpmlId(matVo.getDrugId());
        vo1.setCenterYpmc(matVo.getMatName());
        vo1.setCenterGgid(matVo.getMatSpeId());
        vo1.setCenterYpgg(matVo.getMatSpeName());
        vo1.setCdidCenter(matVo.getMatOriginId());
        vo1.setCenterYplx(matVo.getMatType());
        vo1.setCenterStoreId(matVo.getPhaId());
        vo1.setMatType(matVo.getMatType());
//        vo1.setZhuanhuanxs(matVo.getConversionFactor());
        vo1.setZhuanhuanxs(matVo.getConversionFactorHis());
//        vo1.setDose(matVo.getMatDose());
//        vo1.setDose(matVo.getMatDoseHis());
        vo1.setMatDose(StringUtils.isBlank(matVo.getMatOnceDose())?null:new BigDecimal(matVo.getMatOnceDose()));//中药制剂
//        vo1.setMatDoseunit(StringUtils.isBlank(matVo.getMatDoseUnitHis()) ? null : matVo.getMatDoseUnitHis());
        vo1.setMatDoseunit(StringUtils.isBlank(matVo.getMatOnceDoseUnit()) ? null : matVo.getMatOnceDoseUnit());
        vo1.setCenterKucunsl( null == matVo.getStockNum() ? null : matVo.getStockNum());
        vo1.setCenterYpdm(matVo.getMatPriceId());
        vo1.setMatId(matVo.getkMatId());
        vo1.setMatName(matVo.getkMatName());
        vo1.setHasMore(matVo.getHasMore() ? 1 : 0);
        vo1.setHasMoreTip(matVo.getHasMoreTip());
        vo1.setYaopindmTy(vo1.getHisYpdm());
        vo1.setCenterYpdw(matVo.getMatUnit());
        vo1.setHisYpdw(matVo.getMatUnitHis());
        vo1.setHisYpdj(  StringUtils.isBlank(matVo.getSmallRetailPriceHis())?null : new BigDecimal(matVo.getSmallRetailPriceHis()));
        vo1.setCenterYpdj(StringUtils.isBlank(matVo.getSmallRetailPrice())?null:new BigDecimal(matVo.getSmallRetailPrice()) );
//        vo1.setMatDose(matVo.getMatDose());
        vo1.setShowName(matVo.getMatName());
        vo1.setTcxs(StringUtils.isBlank(matVo.getPurificationFactor())?null : new BigDecimal(matVo.getPurificationFactor()));
        vo1.setNotPayAlone(matVo.getNotPayAloneHis());
        vo1.setXzsm(matVo.getXzsm());
        vo1.setMaxdose(matVo.getMaxdose());
        vo1.setMindose(matVo.getMindose());
        return vo1;
    }
    public static MatVo transCenterHisMappingVoToMatVo(MatVo matVo, CenterHisMappingVO vo1) {
        matVo.setMatPriceIdHis(vo1.getHisYpdm());
        matVo.setDrugIdHis(vo1.getHisYpmlId()) ;
        matVo.setMatNameHis(vo1.getHisYpmc()) ;
        matVo.setMatSpeIdHis(vo1.getHisGgid());
        matVo.setMatSpeNameHis(vo1.getHisYpgg());
        //matVo.setMatTypeHis(null == vo1.getHisYplx() ? null : vo1.getHisYplx().toString()) ;
        matVo.setMatTypeHis(vo1.getCenterYplx()) ;

        matVo.setIsMedicalHis(vo1.getIsInsurance()) ;
        matVo.setUsageCodeHis(vo1.getUsageCode()) ;
        matVo.setMatUsageHis(vo1.getUsage()) ;

        matVo.setApprovalNumberHis(vo1.getApprovalNumber()) ;
        matVo.setDailyMaxDoseInHis(vo1.getDailyMaxDoseIn()) ;
        matVo.setDailyMaxDoseExtHis(vo1.getDailyMaxDoseExt()) ;
        matVo.setDailyMaxNumPrepHis(null == vo1.getDailyMaxNumPrep() ? null : vo1.getDailyMaxNumPrep().toString());
        matVo.setFrequencyIdHis(vo1.getFrequencyId()) ;
        matVo.setFrequencyHis(vo1.getFrequency()) ;
        matVo.setFrequencyRateHis(vo1.getFrequencyRate()) ;
        matVo.setDrugId(vo1.getCenterYpmlId()) ;
        matVo.setMatName(vo1.getCenterYpmc()) ;
        matVo.setMatSpeId(vo1.getCenterGgid()) ;
        matVo.setMatSpeName(vo1.getCenterYpgg()) ;
        matVo.setMatOriginId(vo1.getCdidCenter()) ;
        matVo.setMatType(vo1.getCenterYplx()) ;
        matVo.setPhaId(vo1.getCenterStoreId());
        matVo.setConversionFactor(vo1.getZhuanhuanxs()) ;
        matVo.setMatDose(vo1.getDose()) ;
        matVo.setMatDoseHis(vo1.getDose());
        matVo.setMatDoseUnit(null == vo1.getMatDoseunit() ? null : vo1.getMatDoseunit()) ;
        matVo.setStockNum(null == vo1.getCenterKucunsl() ? null :  vo1.getCenterKucunsl()) ;
        matVo.setMatPriceId(vo1.getCenterYpdm());
        matVo.setkMatId(vo1.getMatId()); ;
        matVo.setkMatName(vo1.getMatName()); ;
        if (null != vo1.getHasMore()){
            matVo.setHasMore( vo1.getHasMore() == 1);
        }
        matVo.setHasMoreTip(vo1.getHasMoreTip()) ;
        return matVo;
    }

    public static TDisplay transBeanToDisplay(HTTPDisPlay vo1, TDisplay tDisplay) {
        tDisplay.setDisplayId(vo1.getId());
        tDisplay.setStoreId(vo1.getPhaId());
        tDisplay.setPreMatType(vo1.getMatType());
        return null;
    }

    public static CenterHisMappingVO transferMatBean(MatVo matVo,CenterHisMappingVO vo1,String source,boolean fromKnow) {
        vo1.setHisYpdm(matVo.getMatPriceIdHis()) ;
        vo1.setHisYpmlId(matVo.getDrugIdHis());
        vo1.setHisYpmc(matVo.getMatNameHis());
        vo1.setHisGgid(matVo.getMatSpeIdHis());
        vo1.setHisYpgg(matVo.getMatSpeNameHis());
        vo1.setHisYplx(StringUtils.isBlank(matVo.getMatTypeHis()) ? null : Integer.parseInt(matVo.getMatTypeHis()));
        vo1.setMatDoseList(matVo.getMatDoseList());
        vo1.setIsInsurance(matVo.getIsMedicalHis());


        if( fromKnow ){

            if (StringUtils.isNotBlank(source)){
                if ("his".equals(source)){
                    vo1.setUsageCode(matVo.getUsageCodeHis());
                    vo1.setUsage(matVo.getMatUsageHis());
                }
                else if ("know".equals(source)){
                    vo1.setUsage( matVo.getKMatYf());
                }else {
                    vo1.setUsageCode("");
                    vo1.setUsage("");
                }
            }
        }else {
            vo1.setUsageCode(null);
            vo1.setUsage(null);
        }



        vo1.setCenterYpcd(matVo.getMatOriginName());
        vo1.setApprovalNumber(matVo.getApprovalNumberHis());
        vo1.setDailyMaxDoseIn(matVo.getDailyMaxDoseInHis());
        vo1.setDailyMaxDoseExt(matVo.getDailyMaxDoseExtHis());
        vo1.setDailyMaxNumPrep(StringUtils.isBlank(matVo.getDailyMaxNumPrepHis()) ? null : Double.parseDouble(matVo.getDailyMaxNumPrepHis()));
        vo1.setFrequencyId(matVo.getFrequencyIdHis());
        vo1.setFrequency(matVo.getFrequencyHis());
        vo1.setFrequencyRate(matVo.getFrequencyRateHis());
        vo1.setCenterYpmlId(matVo.getDrugId());
        vo1.setCenterYpmc(matVo.getMatName());
        vo1.setCenterGgid(matVo.getMatSpeId());
        vo1.setCenterYpgg(matVo.getMatSpeName());
        vo1.setCdidCenter(matVo.getMatOriginId());
        vo1.setCenterYplx(matVo.getMatType());
        vo1.setCenterStoreId(matVo.getPhaId());
//        vo1.setZhuanhuanxs(matVo.getConversionFactor());
        vo1.setZhuanhuanxs(matVo.getConversionFactorHis());
//        vo1.setDose(matVo.getMatDose());
//        vo1.setDose(matVo.getMatDoseHis());
        vo1.setMatDose(StringUtils.isBlank(matVo.getMatOnceDose())?null:new BigDecimal(matVo.getMatOnceDose()));//中药制剂
//        vo1.setMatDoseunit(StringUtils.isBlank(matVo.getMatDoseUnitHis()) ? null : matVo.getMatDoseUnitHis());
        vo1.setMatDoseunit(StringUtils.isBlank(matVo.getMatOnceDoseUnit()) ? null : matVo.getMatOnceDoseUnit());
        vo1.setCenterKucunsl( null == matVo.getStockNum() ? null : matVo.getStockNum());
        vo1.setCenterYpdm(matVo.getMatPriceId());
        vo1.setMatId(matVo.getkMatId());
        vo1.setMatName(matVo.getkMatName());
        vo1.setHasMore(matVo.getHasMore() ? 1 : 0);
        vo1.setHasMoreTip(matVo.getHasMoreTip());
        vo1.setYaopindmTy(vo1.getHisYpdm());
        vo1.setCenterYpdw(matVo.getMatUnit());
        vo1.setHisYpdw(matVo.getMatUnitHis());
        vo1.setHisYpdj(  StringUtils.isBlank(matVo.getSmallRetailPriceHis())?null : new BigDecimal(matVo.getSmallRetailPriceHis()));
        vo1.setCenterYpdj(StringUtils.isBlank(matVo.getSmallRetailPrice())?null:new BigDecimal(matVo.getSmallRetailPrice()) );
//        vo1.setMatDose(matVo.getMatDose());
        vo1.setShowName(matVo.getMatName());
        vo1.setTcxs(StringUtils.isBlank(matVo.getPurificationFactor())?null : new BigDecimal(matVo.getPurificationFactor()));
        vo1.setNotPayAlone(matVo.getNotPayAloneHis());
        vo1.setXzsm(matVo.getXzsm());
        vo1.setMaxdose(matVo.getMaxdose());
        vo1.setMindose(matVo.getMindose());
        return vo1;
    }
}
