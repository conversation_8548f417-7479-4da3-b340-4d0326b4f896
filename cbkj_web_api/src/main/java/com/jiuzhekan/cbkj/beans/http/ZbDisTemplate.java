package com.jiuzhekan.cbkj.beans.http;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "专病模板",description = "国医大师的专病模板")
public class ZbDisTemplate implements Serializable {

    public ZbDisTemplate(String htmlJson, String jsJson) {
        this.htmlJson = htmlJson;
        this.jsJson = jsJson;
    }

    public ZbDisTemplate() {
    }

    @ApiModelProperty(value = "")
    private String htmlJson;

    @ApiModelProperty(value = "")
    private String jsJson;
}