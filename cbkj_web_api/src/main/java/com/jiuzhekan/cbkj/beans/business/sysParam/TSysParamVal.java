package com.jiuzhekan.cbkj.beans.business.sysParam;

import com.jiuzhekan.cbkj.beans.business.TSysParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@ApiModel
public class TSysParamVal implements Serializable {

    @ApiModelProperty("参数代码")
    private String parValues;

    @ApiModelProperty("参数类型（0系统默认 1医联体 2医疗机构 3科室）")
    private Integer type;

    @ApiModelProperty("使用范围（appId, insCode, deptId）")
    private List<AppInsDeptVal> valList;


    public TSysParamVal(String parValues, Integer type, List<TSysParam> list) {
        this.parValues = parValues;
        this.type = type;
        this.valList = list.stream().map(p -> new AppInsDeptVal(p.getAppId(), p.getInsCode(), p.getDeptId())).collect(Collectors.toList());
    }

}