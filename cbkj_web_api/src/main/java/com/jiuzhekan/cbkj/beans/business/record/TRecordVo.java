package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: pre_api
 * @description: 就诊记录表
 * @author: wangtao
 * @create: 2021-03-29 17:31
 **/
@Data
public class TRecordVo implements Serializable {
    private  String recId;
    private String patientContent;//主诉
    private String nowDesc;//现病史
    private String pastDesc;//既往史
    private String fourDiagnosis;//中医四诊
    private String physical;//体格检查
    private String auxiliaryExam;//辅助检查
    private String allergyDesc;//过敏史
    private String personalDesc;//个人史
    /**
     * 治疗意见
     */
    private String treatmentAdvice;
    @TianAnDecryptField
    private String patientName;
    private String patientGender;
    private String recAge1;
    private String recAgeUnit1;

    private String patientCertificateType;
    @TianAnDecryptField
    private String patientCertificate;
    private String deptName;
    private String docId;
    private String nameZh;
    private String disName;
    private String disId;
    private String symName;
    private String symId;
    private String westernDisease;
    private String theNames;
    private String appId;
    private String insCode;
    private String insName;
    List<PrescriptionMin> prescriptionMinList;

}
