package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TRecord2Tag implements Serializable{

    @ApiModelProperty(value = "标签编号")
    private String tagNo;

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "标签首字母拼音")
    private String tagPy;

    @ApiModelProperty(value = "医生工号")
    private String doctorId;

    @ApiModelProperty(value = "医生名称")
    private String doctorName;

    @ApiModelProperty(value = "医共体id")
    private String appId;

    @ApiModelProperty("主诉")
    private String patientContent;

    @ApiModelProperty("现病史")
    private String nowDesc;

    @ApiModelProperty("既往史")
    private String pastDesc;

    @ApiModelProperty("体格检查")
    private String physical;

    @ApiModelProperty("辅助检查")
    private String auxiliaryExam;

    @ApiModelProperty("治疗意见")
    private String treatmentAdvice;

    @ApiModelProperty("身高")
    private String patientHeight;

    @ApiModelProperty("体重")
    private String patientWeight;

    @ApiModelProperty(value = "医疗机构编号")
    private String insId;

    @ApiModelProperty(value = "西医诊断标准编码")
    private String xyzdbzbm;

    @ApiModelProperty(value = "西医诊断标准名称")
    private String xyzdbzmc;

    @ApiModelProperty(value = "中医病名代码")
    private String zybmdm;

    @ApiModelProperty(value = "中医病名名称")
    private String zybmmc;

    @ApiModelProperty(value = "中医证候代码")
    private String zyzhdm;

    @ApiModelProperty(value = "中医证候名称")
    private String zyzhmc;

    @ApiModelProperty(value = "治则治法代码")
    private String zzzfdm;

    @ApiModelProperty(value = "治则治法名称")
    private String zzzfmc;

    @ApiModelProperty(value = "状态【0:可用，1:未保存，2:禁用】")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;
    private String tagType;
    private String tagTypeTips;
    private String version;


}
