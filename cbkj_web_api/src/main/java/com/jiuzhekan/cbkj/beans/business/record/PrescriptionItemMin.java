package com.jiuzhekan.cbkj.beans.business.record;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: pre_api
 * @description: 就诊记录用小明细表
 * @author: wangtao
 * @create: 2021-03-29 17:46
 **/
@Data
public class PrescriptionItemMin implements Serializable {
    private String matId;
    private String matName;
    private  String ypdmHis;
    private String ypmcHis;
    private String ypdmCenter;
    private  String ypmcCenter;
    private String ypmlCenter;
    private String ypmlHis;
    private  String yaopindmTy;
    private String ypggHis;
    private String ypggCenter;
    private  String cdidCenter;
    private String cdmcCenter;
    private String matDose;
    private  String matXsj;
    private String matSeqn;
    private String zhuanhuanxs;
    private  String centerStoreId;
    private  String yfidHis;
    private String yfmcCenter;
    private String yfidCenter;
    private  String yfmcHis;
    private String bzdwHis;
    private String remark;
}
