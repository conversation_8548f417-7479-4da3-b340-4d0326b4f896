package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.beans.business.his.THisRecord;
import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel(value = "挂号信息")
public class TRegister implements Serializable {
    @ApiModelProperty(value = "挂号ID（时间戳）")
    private String registerId;
    @ApiModelProperty(value = "APPID", required = true)
    private String appId;
    @ApiModelProperty(value = "医疗机构代码", required = true)
    private String insCode;
    @ApiModelProperty(value = "患者ID", required = true)
    private String patientId;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "就诊科室ID")
    private String deptId;
    @ApiModelProperty(value = "就诊科室名称")
    private String deptName;
    @ApiModelProperty(value = "就诊医生ID")
    private String doctorId;
    @ApiModelProperty(value = "就诊医生姓名")
    private String doctorName;
    @ApiModelProperty(value = "挂号时间", required = true)
    private Date registerTime;
    @ApiModelProperty(value = "就诊号")
    private Integer registerNum;
    @ApiModelProperty(value = "挂号时间段（1上午  2下午）", required = true)
    private String registerTimeArange;
    @ApiModelProperty(value = "就诊状态（0挂号未缴费，2待就诊，4已就诊，5已付款，6已退款，8已发药）")
    private Integer registerDiagnosisStatus;
    @ApiModelProperty(value = "门诊类型（1门诊 2住院）")
    private Integer clinicTypeId;
    @ApiModelProperty(value = "门诊类型名称")
    private String clinicTypeName;
    @ApiModelProperty(value = "门诊号")
    private String clinicTypeNo;
    @ApiModelProperty(value = "挂号费")
    private Double clinicTypeMoney;
    @ApiModelProperty(value = "开方系统打开时间(修改处方时填，新开处方不填)")
    private Date recOpenTime;
    @ApiModelProperty(value = "HIS患者就诊序号")
    private String visitNo;
    @ApiModelProperty(value = "患者是否怀孕（Y为是，N为否）")
    private String gravidity;

    @ApiModelProperty(value = "互联网标志，1是0否")
    private Integer isInternet;


    @ApiModelProperty(value = "患者")
    private TPatients patients;
    @ApiModelProperty(value = "患者年龄")
    private TPatientAge patientAge;


    @ApiModelProperty(value = "his记录")
    private THisRecord hisRecord;
}