package com.jiuzhekan.cbkj.beans.business.record;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel(value = "常见问题类" ,description = "常见问题类")
public class TBusinessQuestion implements Serializable {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "编号")
    private Integer questionNum;

    @ApiModelProperty(value = "问题标题")
    private String questionTitle;

    @ApiModelProperty(value = "问题答案")
    private String questionAnswer;

    @ApiModelProperty(value = "问题关键词(模糊查询会根据这个参数去标题,答案,关键词里查询符合的数据)")
    private String questionKey;

    @ApiModelProperty(value = "查看人次")
    private Integer questionSeeTimes;

    @ApiModelProperty(value = "点赞人数")
    private Integer questionPraiseTimes;

    @ApiModelProperty(value = "状态：0可用，1禁用")
    private Byte questionState;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private Byte isDel;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者ID")
    private String createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改者ID")
    private String updateUserId;

    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

    @ApiModelProperty(value = "删除者ID")
    private String deleteUserId;

    /**
     * @Description : 新的问题
     * <AUTHOR> xhq
     * @updateTime : 2020/3/3 11:51
     */
    @ApiModelProperty(value = "是否有帮助  (0是,1否, *** 用于初始状态)")
    private String helpful;
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;
}