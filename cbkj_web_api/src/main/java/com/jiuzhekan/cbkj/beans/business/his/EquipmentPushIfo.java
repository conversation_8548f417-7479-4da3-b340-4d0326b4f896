package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class EquipmentPushIfo {
    private Integer pushId;

    @ApiModelProperty("1.推给his 2.推给云系统 3.推给脉象仪")
    private Integer sendType;

    @ApiModelProperty("1.未推送 2.已推送  10.推送失败")
    private Integer sendStatus;

    private Data sendTime;

    private String failInfo;


}
