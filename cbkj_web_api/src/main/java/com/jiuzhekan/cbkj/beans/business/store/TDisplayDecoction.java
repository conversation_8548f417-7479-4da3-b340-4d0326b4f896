package com.jiuzhekan.cbkj.beans.business.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("药房配置代煎信息表")
public class TDisplayDecoction implements Serializable {

    @ApiModelProperty("配置ID")
    private String setId;

    @ApiModelProperty(value = "机构药房配置ID")
    private String displayId;

    @ApiModelProperty(value = "门诊,住院（1门诊，2住院）")
    private String outpatientOrHospitalization;

    @ApiModelProperty(value = "代煎配置是否开启（0是，1否）")
    private String showDecoction;

    @ApiModelProperty(value = "是否默认代煎（0是，1否）")
    private String isDecoction;

    @ApiModelProperty(value = "代煎是否禁用（0是，1否）")
    private String isDecoctionMust;

    @ApiModelProperty(value = "是否显示代煎贴数（0是，1否）")
    private String isDecoctionNum;

    @ApiModelProperty(value = "可代煎最低贴数(默认0)")
    private Integer leastDecoction;

    @ApiModelProperty(value = "通用代煎费用配置（0默认，1配置）")
    private String usuallyDecoctionSet;

    @ApiModelProperty(value = "配方代煎费用配置（0默认，1配置）")
    private String formulaDecoctionSet;


    @ApiModelProperty(value = "药房配置费用明细")
    private List<TDisplayMoneySetting> settingList;

    @ApiModelProperty(value = "通用费用明细")
    private List<TDisplayMoneySetting> usuallyMoneySetting;

    @ApiModelProperty(value = "配方费用明细")
    private List<TDisplayMoneySetting> formulaMoneySetting;
}
