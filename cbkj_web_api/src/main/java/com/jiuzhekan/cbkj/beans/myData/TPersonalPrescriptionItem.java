package com.jiuzhekan.cbkj.beans.myData;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "个人协定方明细")
public class TPersonalPrescriptionItem implements Serializable {

    @ApiModelProperty(value = "协定方明细ID")
    private String persPreItemId;

    @ApiModelProperty(value = "协定方ID")
    private String persPreId;

    @ApiModelProperty(value = "HIS药品目录代码")
    private String ypmlHis;

    @ApiModelProperty(value = "HIS药品规格代码")
    private String ypggdmHis;

    @ApiModelProperty(value = "中心药品目录代码")
    private String ypmlCenter;

    @ApiModelProperty(value = "中心药品规格代码")
    private String ypggdmCenter;

    @ApiModelProperty(value = "知识库药品ID")
    private String matId;

    @ApiModelProperty(value = "药品代码（存HIS的）")
    private String ypdmHis;

    @ApiModelProperty(value = "药品名称（存HIS的）")
    private String ypmcHis;

    @ApiModelProperty(value = "药品规格（存HIS的）")
    private String ypggHis;

    @ApiModelProperty(value = "包装单位（存HIS的）")
    private String bzdwHis;

    @ApiModelProperty(value = "剂量")
    private BigDecimal matDose;

    @ApiModelProperty(value = "用法ID（存HIS的）")
    private String yfidHis;

    @ApiModelProperty(value = "用法名称（存HIS的）")
    private String yfmcHis;

    @ApiModelProperty(value = "序号")
    private Integer matSeqn;

    @ApiModelProperty(value = "转化系数", required = true)
    private Short zhuanhuanxs;


}