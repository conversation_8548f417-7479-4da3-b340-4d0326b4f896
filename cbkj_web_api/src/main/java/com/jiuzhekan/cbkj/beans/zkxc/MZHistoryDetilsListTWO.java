package com.jiuzhekan.cbkj.beans.zkxc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/24 10:24
 * @Version 1.0
 */
@Data
public class MZHistoryDetilsListTWO {

    @ApiModelProperty(value = "波动图")
    private String pulseQuantificationImg;

    private Integer collectId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;


    private MZHistoryDAOTWO leftChi;
    private MZHistoryDAOTWO leftCun;
    private MZHistoryDAOTWO leftGuan;
    private MZHistoryDAOTWO rightCun;
    private MZHistoryDAOTWO rightGuan;
    private MZHistoryDAOTWO rightChi;
    private String totalDescription;
}
