package com.jiuzhekan.cbkj.beans.statistics;

import com.alibaba.fastjson.JSONArray;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: pre_api
 * @description: 拓展表
 * @author: wangtao
 * @create: 2021-03-26 15:56
 **/
@Data
@NoArgsConstructor
public class TPrescriptionChargedNumberVo implements Serializable {
    private String preTimeBegin;
    private String preTimeEnd;
    List<TPreChargedNumVO> voList;
    Map<String, List<Integer>> mergeMap;

    public TPrescriptionChargedNumberVo(String preTimeBegin, String preTimeEnd, List<TPreChargedNumVO> voList) {
        this.preTimeBegin = preTimeBegin;
        this.preTimeEnd = preTimeEnd;
        this.voList = voList;
    }

    public Map<String, List<Integer>> mergeVoList() {
        mergeMap = new HashMap<>();
        if (voList == null || voList.isEmpty()) {
            return mergeMap;
        }

        for (int i = 0; i < voList.size(); i++) {

            TPreChargedNumVO numVO = voList.get(i);

            List<Integer> insNameRange = mergeMap.get(numVO.getInsName());
            insNameRange = initRange(insNameRange, i, 0);
            mergeMap.put(numVO.getInsName(), insNameRange);

            List<Integer> preMzZyRange = mergeMap.get(numVO.getInsName() + numVO.getPreMzZy());
            preMzZyRange = initRange(preMzZyRange, i, 1);
            mergeMap.put(numVO.getInsName() + numVO.getPreMzZy(), preMzZyRange);

            List<Integer> deptNameRange = mergeMap.get(numVO.getInsName() + numVO.getPreMzZy() + numVO.getDeptName());
            deptNameRange = initRange(deptNameRange, i, 2);
            mergeMap.put(numVO.getInsName() + numVO.getPreMzZy() + numVO.getDeptName(), deptNameRange);

            List<Integer> insTotalRange = mergeMap.get(numVO.getInsName() + "total");
            insTotalRange = initRange(insTotalRange, i, 5);
            mergeMap.put(numVO.getInsName() + "total", insTotalRange);
        }
        return mergeMap;
    }

    private List<Integer> initRange(List<Integer> range, int row, int col) {
        if (range == null) {
            range = new ArrayList<>();
            range.add(row);
            range.add(row);
            range.add(col);
            range.add(col);
        } else {
            range.set(1, row);
        }
        return range;
    }
}
