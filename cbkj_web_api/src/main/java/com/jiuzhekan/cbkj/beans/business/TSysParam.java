package com.jiuzhekan.cbkj.beans.business;

import com.alibaba.fastjson.JSONArray;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TSysParam implements Serializable {

    private String parId;

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医疗机构代码（000000代表该医联体中所有的医疗机构）")
    private String insCode;

    @ApiModelProperty("科室ID")
    private String deptId;

    @ApiModelProperty("参数代码")
    private String parCode;

    @ApiModelProperty("参数名称")
    private String parName;

    @ApiModelProperty("参数值")
    private String parValues;

    @ApiModelProperty("参数说明")
    private String parDes;

    private Date createDate;

    private String createUser;

    private String createUsername;

    private Date updateDate;

    private String updateUser;

    private String updateUsername;

    private Date delDate;

    private String delUser;

    private String delUsername;

    private String isDel;

    private Integer seqn;
    //PAR_CLASSIFY
    private String parClassify;
    //PAR_NUMBER
    private String parNumber;


    private String insName;

    private String appName;

    private String updateDateStr;

    private JSONArray extendArr;

    public TSysParam(String parCode) {
        this.parCode = parCode;
    }


    public TSysParam(String parId, String parCode, String parName, String parDes) {
        this.parId = parId;
        this.parCode = parCode;
        this.parName = parName;
        this.parDes = parDes;
        initCreate();
    }

    public TSysParam(String parId, String parCode, String parName, String parDes, String parValues, String parClassify, String parNumber) {
        this.parId = parId;
        this.parCode = parCode;
        this.parName = parName;
        this.parDes = parDes;
        this.parValues = parValues;
        this.parNumber = parNumber;
        this.parClassify = parClassify;
        initCreate();
    }

    public TSysParam(String parId, String parCode, String parName, String parDes, String parValues, String appId, String insCode, String deptId) {
        this.parId = parId;
        this.parCode = parCode;
        this.parName = parName;
        this.parDes = parDes;
        this.parValues = parValues;
        this.appId = appId;
        this.insCode = insCode;
        this.deptId = deptId;
        initCreate();
    }

    private void initCreate() {
        this.createDate = new Date();
        this.createUser = AdminUtils.getCurrentHr().getId();
        this.createUsername = AdminUtils.getCurrentHr().getNameZh();
        this.isDel = Constant.BASIC_STRING_ZERO;
    }
}