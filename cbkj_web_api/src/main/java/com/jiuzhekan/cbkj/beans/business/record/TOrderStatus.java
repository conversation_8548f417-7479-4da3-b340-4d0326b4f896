package com.jiuzhekan.cbkj.beans.business.record;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuzhekan.cbkj.common.constant.OrderStatusConstant;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel("订单状态表")
public class TOrderStatus implements Serializable {

    @ApiModelProperty(value = "订单状态ID")
    private String statusId;

    @ApiModelProperty(value = "挂号ID（没有的填0）")
    private String registerId;

    @ApiModelProperty(value = "处方ID（没有的填0）")
    private String preId;

    @ApiModelProperty(value = "操作人ID")
    private String operationUserid;

    @ApiModelProperty(value = "操作人姓名")
    private String operationUsername;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "物流查询地址页面")
    private String expressageUrl;


    @ApiModelProperty(value = "业务操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    @ApiModelProperty(value = "操作类型（3预约挂号  5挂号缴费  7咨询缴费  10开方 20删除 30审核通过 " +
            "40审核未通过 50处方缴费 80配药 90发药 100取消发药 110退药 111退药 120取消退药 130煎药 140配送 150收货）")
    private Integer operationType;
    private String operationTypeName;
    @ApiModelProperty(value = "内容（140配送填  物流公司编号_运单号）")
    private String operationContent;

    @ApiModelProperty(value = "订单状态表的记录插入时间,查询按此排序")
    private Date createTime;

    public TOrderStatus (String preId, OrderStatusConstant operation) {
        this.setStatusId(IDUtil.getID());
        this.setRegisterId("0");
        this.setPreId(preId);
        this.setOperationUserid(AdminUtils.getCurrentHr().getId());
        this.setOperationUsername(AdminUtils.getCurrentHr().getNameZh());
        this.setOperationTime(new Date());
        this.setOperationType(operation.getIndex());
        this.setOperationContent(operation.getName());
        this.setCreateTime(new Date());
    }
}