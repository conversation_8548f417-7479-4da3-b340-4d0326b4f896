package com.jiuzhekan.cbkj.beans.business.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcEquipment implements Serializable{

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "设备mac地址")
    private String macAddress;

    @ApiModelProperty(value = "设备名字")
    private String equipmentName;
    @ApiModelProperty(value = "默认设备 0默认 1不是",hidden = true)
    private int defaultIp;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "1.四诊仪2.经络仪3.热成像仪4.脉象复现仪")
    private Integer equipmentType;

    @ApiModelProperty(value = "是否默认展示【0默认1不是】")
    private Integer isDefault;


    private String insCode;
    private String appId;
    private String appName;
    private String insName;

}
