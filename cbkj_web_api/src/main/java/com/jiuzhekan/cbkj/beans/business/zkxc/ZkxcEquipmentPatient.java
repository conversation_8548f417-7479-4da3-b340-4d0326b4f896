package com.jiuzhekan.cbkj.beans.business.zkxc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcEquipmentPatient implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "检查时间")
    private Date checkTime;

    @ApiModelProperty(value = "mac地址")
    private String macAddress;

    @ApiModelProperty(value = "（1上午 2下午）")
    private Byte checkRange;

    @ApiModelProperty(value = "")
    private String name;

    @ApiModelProperty(value = "0女1男")
    private String sex;

    @ApiModelProperty(value = "")
    private Integer age;

    @ApiModelProperty(value = "")
    private String phone;

    @ApiModelProperty(value = "")
    private String idCard;
    @ApiModelProperty(value = "挂号id")
    private String registerId;

    @ApiModelProperty(value = "（新华：以下字段是） -1.待分配设备 0.等待设备数据 1.获取到设备数据 2.已推送给HIS  10.推送失败")
    private Integer infoStatus;
    @ApiModelProperty(value = "推送失败原因")
    private String failInfo;

    @ApiModelProperty(value = "HIS诊序号")
    private String visitNo;

    @ApiModelProperty(value = "就诊卡号")
    private String medicalCardNo;
    private String appId;
    private String insCode;
    @ApiModelProperty(value = "（1.四诊仪2.经络仪3.热成像仪4.脉象复现仪）")
    private Integer equipmentType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    @ApiModelProperty(hidden = true)
    private Integer patientSource;
    private Date registerTime;
    private Date receiveTime;
    private Integer collectId;

    @ApiModelProperty(value ="病历复合 0 未符合 1 复合")
    private Integer compositeMedical;

    @ApiModelProperty(value ="预问诊（当天做过就行区分上午下午） 0 未完成 1完成")
    private Integer diagnosisStatus;

    @ApiModelProperty(value ="体格检查状态 0 未检查 1 检查")
    private Integer physiqueStatus;
    @ApiModelProperty(value ="病历复合时间")
    private Date compositeTime;

    @ApiModelProperty(value ="病历复合医生姓名")
    private String compositeName;

    @ApiModelProperty(value ="病历复合医生id")
    private String compositeId;
    private String deptId;
    private Date orderTime;


}
