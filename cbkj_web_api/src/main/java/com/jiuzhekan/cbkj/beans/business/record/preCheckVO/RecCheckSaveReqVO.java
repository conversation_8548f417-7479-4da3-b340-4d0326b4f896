package com.jiuzhekan.cbkj.beans.business.record.preCheckVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "处方审核保存请求类")
public class RecCheckSaveReqVO implements Serializable {
    @ApiModelProperty(value = "病历id",required = true)
    private String recId;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "病历审核不通过原因")
    private String checkAdvise;

    @ApiModelProperty(value = "各个处方的审核信息集合")
    private List<PreSaveReqVO> preList;
}