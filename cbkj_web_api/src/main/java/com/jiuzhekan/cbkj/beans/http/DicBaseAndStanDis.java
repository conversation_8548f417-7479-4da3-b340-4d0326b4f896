package com.jiuzhekan.cbkj.beans.http;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/11 11:47
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@ApiModel
public class DicBaseAndStanDis implements Serializable {
    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "疾病ID-HIS")
    private String disIdHis;

    @ApiModelProperty(value = "疾病ID-SYS")
    private String disIdSys;

    @ApiModelProperty(value = "疾病编码-HIS")
    private String disCodeHis;

    @ApiModelProperty(value = "疾病编码-SYS")
    private String disCodeSys;

    @ApiModelProperty(value = "疾病名称-HIS")
    private String disNameHis;

    @ApiModelProperty(value = "疾病名称-SYS")
    private String disNameSys;


    @ApiModelProperty(value = "证型ID-HIS")
    private String symIdHis;

    @ApiModelProperty(value = "证型ID-SYS")
    private String symIdSys;

    @ApiModelProperty(value = "证型编码-HIS")
    private String symCodeHis;

    @ApiModelProperty(value = "证型编码-SYS")
    private String symCodeSys;

    @ApiModelProperty(value = "证型名称-HIS")
    private String symNameHis;

    @ApiModelProperty(value = "证型名称-SYS")
    private String symNameSys;
}
