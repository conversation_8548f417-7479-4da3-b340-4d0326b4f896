package com.jiuzhekan.cbkj.beans.statistics;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 描述
 * 二十四节气不同性别发病率
 *
 * <AUTHOR>
 * @Date 2020/7/16
 */
@Data
public class TStatistics  implements Serializable {

    private String id;
    private String appId;
    private String insCode;
    private Integer timeType;// '时间类型,1昨天,2近半月,3近一个季度,4近一年',
    private Integer statisType;// '统计类型：1高发疾病,2证型分布,3疾病年龄分布,4性别分布,5二十四节气,6上升趋势前5',
    private String disName;// '病/症名称',
    private Integer number;// '数量',
    private String groupName;//'分组名称'
    private Date beginTime;//统计开始日期条件
    private Date endTime;//统计结束日期条件
    private Date createTime;//'创建时间',
    private String createUserId;//'创建者ID',
    private String createUserName;//'创建者姓名',
    private Date updateTime;//'修改时间',
    private String updateUserId;//'修改者ID',
    private Date deleteTime;//'删除时间',
    private String deleteUserId;//'删除者ID',
    private Integer limit;//查询时，查询数量

    private String nowDate;
    /**
     * 病历类型（1门诊 2住院）
     */
    private String recTreType;
    private List<String> dateList;

    private List<TStatistics> statisList;

}