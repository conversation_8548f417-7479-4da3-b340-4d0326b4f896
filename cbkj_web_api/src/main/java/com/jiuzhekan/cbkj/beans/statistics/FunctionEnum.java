package com.jiuzhekan.cbkj.beans.statistics;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * 统计功能项
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/4/15 10:57
 */
public enum FunctionEnum implements Serializable {
    /**
     * 统计功能项
     */
    SETTING_PWD("设置", "修改密码次数", ""),
    SETTING_WB("设置", "五笔使用人数", "", 2),
    SETTING_PY("设置", "拼音使用人数", "", 2),
    SETTING_QUESTION("设置", "常见问题查阅次数", ""),
    SETTING_MANUAL("设置", "操作手册下载量", ""),
    SETTING_PROPOSAL("设置", "建议数", "", true),
    PATIENTS("我的患者", "查看次数", ""),
    ELECTRONIC_RECORD("中医电子病历", "使用次数", ""),
    SYNDROME_AI("智能辨证", "AI辨证次数", "", true),
    SYNDROME_MASTER("智能辨证", "国医大师辨证次数", "", true),
    TREATMENT_SCHEME("智能开方", "病证推导次数", ""),
    TREATMENT_PRE("智能开方", "方解查看次数", ""),
    TREATMENT_MAT("智能开方", "查看中药详情次数", ""),
    TREATMENT_MY_NUM("智能开方", "参考医案-我的医案次数", ""),
    TREATMENT_EXPERT_NUM("智能开方", "参考医案-名家验案次数", ""),
    TREATMENT_MY_DETAIL("智能开方", "我的医案查看次数", ""),
    TREATMENT_EXPERT_DETAIL("智能开方", "名家验案查看次数", ""),
    TREATMENT_SAVE_PERSONAL("智能开方", "“存为协定方”操作次数", "", true),
    TREATMENT_ACU("智能开方", "查看穴位次数（适宜技术）", ""),
    TREATMENT_NUM("智能开方", "就诊记录查看次数", ""),
    TREATMENT_DETAIL("智能开方", "就诊记录-中医电子病历集成视图查看次数", "", true),
    TREATMENT_PRODUCTION("智能开方", "膏方百分率（内服）", "", 3, "内服膏方数量", "内服处方数量"),
    TREATMENT_DECOCTION("智能开方", "代煎百分率（内服）", "", 3, "内服代煎数量", "内服处方数量"),
    TREATMENT_EXPRESS("智能开方", "配送百分率（内服）", "", 3, "内服配送数量", "内服处方数量"),
    ANALYSIS_USAGE("中医体质辨识", "使用次数", ""),
    ANALYSIS_PRINT("中医体质辨识", "打印次数", ""),
    ANALYSIS_NUM("中医体质辨识", "体质辨识数量", ""),
    RECORD_MY_OLD("我的历史病历", "查看次数", ""),
    PERSONAL_PRE_NUM("协定方", "协定方数量", ""),
    PERSONAL_PRE_RATE_SELF("协定方", "个人协定方百分率", "", 3, "个人协定方数量", "协定方数量"),
    PERSONAL_PRE_RATE_DEPT("协定方", "科室协定方百分率", "", 3, "科室协定方数量", "协定方数量"),
    PERSONAL_PRE_AVG_DEPT("协定方", "科室平均协定方", "", 4, "科室协定方数量", "科室数"),
    PERSONAL_PRE_RATE_INS("协定方", "全院协定方百分率", "", 3, "全院协定方数量", "协定方数量"),
    STATISTIC_1("临床业务监管", "医生工作量分析使用次数", ""),
    STATISTIC_2("临床业务监管", "中医疾病谱分析使用次数", ""),
    STATISTIC_3("临床业务监管", "处方金额分析使用次数", ""),
    STATISTIC_4("临床业务监管", "中药处方分析使用次数", ""),
    STATISTIC_5("临床业务监管", "患者分析使用使用次数", ""),
    RECORD_MANAGE("病历管理", "病历查询次数", ""),
    RECORD_TEMPLATE("电子病历模板配置", "电子病历模板数", ""),
    CHECK_PRE_NUM("处方审核", "人工审核处方数", ""),
    CHECK_PRE_RATE("处方审核", "人工审核百分率", "", 3, "人工审核的处方数", "总处方数"),
    EXPERIENCE_NUM("专家经验共享", "专家经验共享条数", ""),
    EXPERIENCE_NUM_IN("专家经验共享", "专家经验共享内服处方数", ""),
    EXPERIENCE_NUM_EXT("专家经验共享", "专家经验共享外用处方数", ""),
    EXPERIENCE_NUM_EXPERT("专家经验共享", "专家经验共享专家数", "", 2),
    VERIFY_INDEX("名医验案", "浏览次数", ""),
    VERIFY_SEARCH("名医验案", "搜索次数", ""),
    VERIFY_DETAIL("名医验案", "查看次数", ""),
    VERIFY_NOTE("名医验案", "笔记数", ""),
    VERIFY_COLLECT("名医验案", "收藏次数", ""),
    MAT_SEARCH("中药查询", "搜索次数", ""),
    MAT_EFFECT("中药查询", "功效查询次数", ""),
    MAT_DETAIL("中药查询", "查看次数", ""),
    MAT_COLLECT("中药查询", "收藏次数", ""),
    PRE_SEARCH("方剂查询", "搜索次数", ""),
    PRE_EFFECT("方剂查询", "功效查询次数", ""),
    PRE_DETAIL("方剂查询", "查看次数", ""),
    PRE_COLLECT("方剂查询", "收藏次数", ""),
    ACU_SEARCH("经络穴位查询", "穴位搜索次数", ""),
    ACU_DETAIL("经络穴位查询", "查看次数", ""),
    ACU_COLLECT("经络穴位查询", "收藏次数", ""),
    DISEASE_SEARCH("疾病查询", "搜索次数", ""),
    DISEASE_DETAIL("疾病查询", "查看次数", ""),
    DISEASE_COLLECT("疾病查询", "收藏次数", "");

    /**
     * 来源
     */
    private String source;
    /**
     * 名称
     */
    private String value;
    /**
     * 显示名称
     */
    private String name;
    /**
     * 1 求和 2 取最后记录 3百分比numerator/denominator 4 平均值 numerator/最后记录
     */
    private int type;
    /**
     * 是否展示图表
     */
    private boolean showChart;
    /**
     * 分子
     */
    private String numerator;
    /**
     * 分母
     */
    private String denominator;

    FunctionEnum(String source, String value, String name) {
        this.source = source;
        this.value = value;
        this.name = name;
        this.type = 1;
        this.showChart = false;
    }

    FunctionEnum(String source, String value, String name, boolean showChart) {
        this.source = source;
        this.value = value;
        this.name = name;
        this.type = 1;
        this.showChart = showChart;
    }

    FunctionEnum(String source, String value, String name, int type) {
        this.source = source;
        this.value = value;
        this.name = name;
        this.type = type;
    }

    FunctionEnum(String source, String value, String name, int type, String numerator, String denominator) {
        this.source = source;
        this.value = value;
        this.name = name;
        this.type = type;
        this.numerator = numerator;
        this.denominator = denominator;
    }


    public String getSource() {
        return source;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return StringUtils.isBlank(name) ? value : name;
    }

    public int getType() {
        return type;
    }

    public boolean isShowChart() {
        return showChart;
    }

    public String getNumerator() {
        return numerator;
    }

    public String getDenominator() {
        return denominator;
    }

    public static FunctionEnum find(String source, String name) {
        for (FunctionEnum fun : FunctionEnum.values()) {
            if (fun.getSource().equals(source) && fun.getName().equals(name)) {
                return fun;
            }
        }
        return null;
    }
}
