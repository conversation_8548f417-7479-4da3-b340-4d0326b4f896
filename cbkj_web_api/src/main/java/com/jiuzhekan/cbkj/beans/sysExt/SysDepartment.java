package com.jiuzhekan.cbkj.beans.sysExt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("科室")
public class SysDepartment implements Serializable {

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty("科室名称")
    private String deptName;

    @ApiModelProperty("上级科室ID")
    private String deptParentId;

    @ApiModelProperty("上级科室")
    private String deptParentName;

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医联体名称")
    private String appName;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty("医疗机构名称")
    private String insName;

    @ApiModelProperty("第三方科室ID")
    private String deptOriginId;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "类别：1 科室、2 病区 默认1")
    private String deptType;

    @ApiModelProperty(value = "sort")
    private Integer sort;

    private String insParentCode;

}