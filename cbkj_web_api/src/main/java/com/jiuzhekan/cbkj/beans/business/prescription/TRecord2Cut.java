package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TRecord2Cut implements Serializable{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "病历编号")
    private String recId;

    @ApiModelProperty(value = "标签编号")
    private String tagNo;

    @ApiModelProperty(value = "脉诊")
    private String qzmz;
    private String qzazdm;

    @ApiModelProperty(value = "脉诊其他")
    private String qzmzqt;

    @ApiModelProperty(value = "脉诊代码")
    private String qzmzdm;

    @ApiModelProperty(value = "按诊")
    private String qzaz;

    @ApiModelProperty(value = "按诊其他")
    private String qzazqt;

    @ApiModelProperty(value = "其他")
    private String qzqt;

    @ApiModelProperty(value = "状态【0:可用，1:禁用】")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    private Integer equipmentPatientId;


}
