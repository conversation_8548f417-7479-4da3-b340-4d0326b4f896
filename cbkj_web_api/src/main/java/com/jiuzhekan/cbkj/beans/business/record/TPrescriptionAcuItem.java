package com.jiuzhekan.cbkj.beans.business.record;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel(value = "适宜技术处方明细表")
public class TPrescriptionAcuItem implements Serializable{
    @ApiModelProperty(value = "适宜技术处方明细表UUID")
    private String acuItemId;
    @ApiModelProperty(value = "处方UUID")
    private String preId;
    @ApiModelProperty(value = "穴位ID", required = true)
    private String acuId;
    @ApiModelProperty(value = "穴位代码", required = true)
    private String acuCode;
    @ApiModelProperty(value = "穴位名称", required = true)
    private String acuName;
    @ApiModelProperty(value = "针数", required = true)
    private Short acuNum;
    @ApiModelProperty(value = "序号", required = true)
    private Integer acuSeqn;
    private Date insertTime;
}