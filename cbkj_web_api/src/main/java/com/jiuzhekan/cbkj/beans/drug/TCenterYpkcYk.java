package com.jiuzhekan.cbkj.beans.drug;

import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionItem;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionPreparationItem;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;


@Data
@NoArgsConstructor
@ApiModel
public class TCenterYpkcYk implements Serializable {

    @ApiModelProperty(value = "预扣库存ID")
    private String whId;

    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "第三方药品代码")
    private String matPriceId;

    @ApiModelProperty(value = "药房ID")
    private String phaId;

    @ApiModelProperty(value = "预扣库存数量（已乘以帖数）")
    private BigDecimal ykStockNum;

    @ApiModelProperty(value = "处方ID")
    private String preId;

    @ApiModelProperty(value = "处方明细ID")
    private String preItemId;

    @ApiModelProperty(value = "处方时间")
    private Date preTime;

    /*** 2023.11.09 预扣按批次处理  ***/

    @ApiModelProperty(value = "批次id")
    private String stoBatchId;
    @ApiModelProperty(value = "批次号")
    private String productionBatch;
    @ApiModelProperty(value = "包装量")
    private BigDecimal matPackMount;
    @ApiModelProperty(value = "药品名称")
    private String matName;
    @ApiModelProperty(value = "批次零售价")
    private BigDecimal retailPrice;
    @ApiModelProperty(value = "批次批发价")
    private BigDecimal purchasePrice;
    @ApiModelProperty(value = "状态（10 已开方 20 已删除（作废） 50已收费 90已发药 100取消发药 110退费 ）")
    private Integer status;
    private String isOpenBatchPrice;  //是否开启批次库存  1开启 0未开启
    /**
     * 中草药预扣库存
     * 按 剂量*帖数 的总数预扣
     *
     * @param pre  pre
     * @param item item
     * <AUTHOR>
     * @date 2021/9/24
     */
    private TCenterYpkcYk(TPrescription pre, TPrescriptionItem item) {
        this.setPreId(pre.getPreId());
        this.setPreTime(pre.getPreTime());
        this.setPreItemId(item.getPreItemId());
        this.setDrugId(item.getYpmlCenter());
        this.setMatPriceId(item.getYpdmCenter());
        this.setMatName(item.getYpmcCenter());
        this.setPhaId(pre.getStoreId());
        this.setYkStockNum(item.getMatDose().multiply(new BigDecimal(pre.getPreNum())));
    }

    /**
     * 中药制剂预扣库存
     *
     * @param pre  pre
     * @param item item
     * <AUTHOR>
     * @date 2021/9/24
     */
    private TCenterYpkcYk(TPrescription pre, TPrescriptionPreparationItem item) {
        this.setPreId(pre.getPreId());
        this.setPreTime(pre.getPreTime());
        this.setPreItemId(item.getPreItemId());
        this.setDrugId(item.getYpmlCenter());
        this.setMatPriceId(item.getYpdmCenter());
        this.setMatName(item.getYpmcCenter());
        this.setPhaId(pre.getStoreId());
        this.setYkStockNum(item.getMatDose());
    }

    /**
     * 处方预扣库存
     *
     * @param pre pre
     * @return java.util.List<com.jiuzhekan.cbkj.beans.drug.TCenterYpkcYk>
     * <AUTHOR>
     * @date 2021/9/23
     */
    public static List<TCenterYpkcYk> trans(TPrescription pre) {
        List<TCenterYpkcYk> ykList = new ArrayList<>();
        if (pre != null) {

            if (Constant.BASIC_STRING_TEN.equals(pre.getPreOrigin())) {
                ykList.addAll(transFormula(pre));
            } else if (Constant.BASIC_STRING_TWELVE.equals(pre.getPreOrigin())) {
                ykList.addAll(transFormula(pre));
            } else {
                ykList.addAll(transItem(pre));
            }
        }
        return ykList;
    }

    public static List<TCenterYpkcYk> transItem(TPrescription pre) {
        List<TCenterYpkcYk> ykList = new ArrayList<>();
        if (pre != null) {
            if (pre.getItemList() != null && !pre.getItemList().isEmpty()) {
                for (TPrescriptionItem item : pre.getItemList()) {
                    ykList.add(new TCenterYpkcYk(pre, item));
                }
            }
            if (pre.getPreparationItemList() != null && !pre.getPreparationItemList().isEmpty()) {
                for (TPrescriptionPreparationItem item : pre.getPreparationItemList()) {
                    ykList.add(new TCenterYpkcYk(pre, item));
                }
            }
        }
        return ykList;
    }


    public static List<TCenterYpkcYk> transFormula(TPrescription pre) {
        List<TCenterYpkcYk> ykList = new ArrayList<>();
        if (pre != null) {
            if (pre.getItemList() != null && !pre.getItemList().isEmpty()) {
                for (TPrescriptionItem item : pre.getItemList()) {
                    TCenterYpkcYk tCenterYpkcYk = new TCenterYpkcYk();
                    //处方id
                    tCenterYpkcYk.setPreId(pre.getPreId());
                    tCenterYpkcYk.setPreTime(pre.getPreTime());
                    //处方明细id
                    tCenterYpkcYk.setPreItemId(pre.getPreOriginId());
                    tCenterYpkcYk.setDrugId(item.getYpmlCenter());
                    //药品代码
                    tCenterYpkcYk.setMatPriceId(pre.getPreOriginId());
                    tCenterYpkcYk.setMatName(pre.getPreOriginName());
                    tCenterYpkcYk.setPhaId(pre.getStoreId());
                    //预扣
                    tCenterYpkcYk.setYkStockNum(new BigDecimal(pre.getPreNum()));
                    ykList.add(tCenterYpkcYk);
                    break;//走一次，配方就一条数据。
                }
            }
        }
        return ykList;
    }


    /**
     * 预扣库存，锁定批次价格，回填处方价格
     * 这种模式药品按总量（剂量*帖数）预扣，所以价格只能按固定方式计算：
     * 单个药的小计=∑(批次价格*批次预扣库存数量)；总金额=∑(单个药的小计)；
     *
     * @param pre      处方
     * @param ykResult 预扣库存结果
     * <AUTHOR>
     * @date 2023/11/14
     */
    public static void backFill(TPrescription pre, List<TCenterYpkcYk> ykResult) {

        if (Constant.BASIC_STRING_TEN.equals(pre.getPreOrigin())) {
            backFillFormula(pre, ykResult);
        } else if (Constant.BASIC_STRING_TWELVE.equals(pre.getPreOrigin())) {
            backFillFormula(pre, ykResult);
        } else {
            backFillItem(pre, ykResult);
        }
    }


    public static void backFillItem(TPrescription pre, List<TCenterYpkcYk> ykResult) {
        //按 处方明细 分组，一个处方明细 可能会对应 多个预扣批次
        Map<String, List<TCenterYpkcYk>> ykMap = new HashMap<>();
        for (TCenterYpkcYk yk : ykResult) {
            List<TCenterYpkcYk> yks = ykMap.computeIfAbsent(yk.getPreItemId(), k -> new ArrayList<>());
            yks.add(yk);
        }
        String isOpenBatchPrice1 = ykResult.get(0).getIsOpenBatchPrice();
        if (StringUtils.isBlank(isOpenBatchPrice1) || Constant.BASIC_STRING_ONE.equals(isOpenBatchPrice1)){
            //单个药的小计 = ∑(批次价格*批次预扣库存数量)
            for (TPrescriptionItem item : pre.getItemList()) {
                //获取到 一个处方明细 对应的 预扣批次集合
                List<TCenterYpkcYk> yks = ykMap.get(item.getPreItemId());
                if (yks != null) {
                    //单个药品的小计，保留两位
                    BigDecimal matTotalPrice = yks.stream()
                            //这个批次的预扣药品金额 = 零售价 * 预扣数量（已乘以帖数），保留两位
                            .map(yk -> yk.getRetailPrice().multiply(yk.getYkStockNum()))
                            //求和
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(2, RoundingMode.HALF_UP);
                    //单个药品总数量
                    BigDecimal matTotalNum = yks.stream()
                            .map(TCenterYpkcYk::getYkStockNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //单个药品平均单价，保留6位
                    BigDecimal matSinglePrice = matTotalPrice.divide(matTotalNum, 6, RoundingMode.HALF_UP);

                    //回填处方明细的销售价和小计
                    item.setMatXsj(matSinglePrice);
                    //药品小计保留两位（参数“计算规则”失效）
                    item.setMatTotalPrice(matTotalPrice.stripTrailingZeros());
                }
            }
        }else {
            for (TPrescriptionItem item : pre.getItemList()) {
                BigDecimal preNumDecimal = new BigDecimal(String.valueOf(pre.getPreNum()));
                item.setMatTotalPrice(item.getMatXsj().multiply(item.getMatDose()).multiply(preNumDecimal).setScale(2, RoundingMode.HALF_UP));
            }
        }


        //总金额=∑(单个药的小计)；
        BigDecimal matTotalMoney = pre.getItemList().stream().map(TPrescriptionItem::getMatTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        pre.setMatTolMoney(matTotalMoney.stripTrailingZeros());
        pre.computePreTolMoney();
    }

    public static void backFillFormula(TPrescription pre, List<TCenterYpkcYk> ykResult) {
        BigDecimal matTotalMoney = ykResult.stream()
                //这个批次的预扣价格 = 零售价 * 预扣数量（已乘以帖数）
                .map(yk -> yk.getRetailPrice().multiply(yk.getYkStockNum()))
                //求和
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        pre.setMatTolMoney(matTotalMoney);
        pre.computePreTolMoney();
    }
}
