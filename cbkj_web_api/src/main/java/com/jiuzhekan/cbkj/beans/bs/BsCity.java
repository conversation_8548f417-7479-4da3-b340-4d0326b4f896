package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("市")
public class BsCity implements Serializable {

    private Integer cityId;

    @ApiModelProperty("省代码")
    private String provinceCode;

    @ApiModelProperty("市代码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("简称")
    private String shortName;

    private String lng;

    private String lat;

    private Integer sort;

    private Date gmtCreate;

    private Date gmtModified;

    private String memo;

    private Integer dataState;


}
