package com.jiuzhekan.cbkj.beans.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcPadRecord implements Serializable{

    @ApiModelProperty(value = "")
    private Integer zkxcPadRecordId;

    @ApiModelProperty(value = "")
    private String patientName;
    private String patientCardNo;

    @ApiModelProperty(value = "（1上午 2下午）")
    private Byte checkRange;

    @ApiModelProperty(value = "")
    private String patientSex;

    @ApiModelProperty(value = "")
    private String patientAge;

    @ApiModelProperty(value = "预诊时间")
    private Date preDiagnosisTime;

    @ApiModelProperty(value = "")
    private String zhuShu;

    @ApiModelProperty(value = "")
    private String xianBingShi;

    @ApiModelProperty(value = "")
    private String jiWangShi;

    @ApiModelProperty(value = "")
    private String tiGeJianCha;

    @ApiModelProperty(value = "")
    private Date createTime;

    @ApiModelProperty(value = "")
    private Integer equipmentPatientId;

    @ApiModelProperty(value = "病历复合 0 未符合 1 复合(预诊推过来按这个字段判断是否覆盖)")
    private Integer compositeMedical;

    @ApiModelProperty(value = "检查时间(日期)")
    private Date checkTime;


    @ApiModelProperty(value = "辅助检查")
    private String fuZhuJianCha;

    @ApiModelProperty(value = "治疗意见")
    private String zhiLiaoYijian;
    @ApiModelProperty(value = "体重")
    private String weight;

    @ApiModelProperty(value = "身高")
    private String height;


}
