package com.jiuzhekan.cbkj.beans.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 14:15
 * @Version 1.0
 */
@Data
@ApiModel
public class MZHistory {
    private Date insertTime;
    @ApiModelProperty(value = "顺序是：左手寸关尺，右手寸关尺,的id主键")
    private String[] zkxcChiCunGuanIds;
    private String[] mzResultType;
//    @ApiModelProperty(value = "左手-尺")
//    private String leftChiPulsePotential;
//    @ApiModelProperty(value = "左手-寸")
//    private String leftCunPulsePotential;
//    @ApiModelProperty(value = "左手-关")
//    private String leftGuanPulsePotential;
//    @ApiModelProperty(value = "右手-尺")
//    private String rightChiPulsePotential;
//
//    @ApiModelProperty(value = "右手-寸")
//    private String rightCunPulsePotential;
//
//    @ApiModelProperty(value = "右手-关")
//    private String rightGuanPulsePotential;

    @ApiModelProperty(value = "顺序是：左手的 寸关尺，右手的 寸关尺")
    private String[] pulsePotentials;
}
