package com.jiuzhekan.cbkj.beans.business.record;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: pre_api
 * @description: 所有的记录
 * @author: wangtao
 * @create: 2021-03-29 14:52
 **/
@Data
public class TRecordAll implements Serializable {
    private String recId;
    private String registerId;
    private String recordTime;
    private  String diseaseName;
    private String recTreType;
    private String isSpecialDis;

}
