package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import springfox.documentation.annotations.ApiIgnore;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 角色
 */
@ApiModel
public class AdminRule implements Serializable {

    private static final long serialVersionUID = -6335654944450560361L;
    @ApiModelProperty(value="角色ID")
    private String rid;

    @ApiModelProperty(value="角色英文名")
    private String rname;

    @ApiModelProperty(value="角色中文名")
    private String rnameZh;

    @ApiModelProperty(value="角色描述")
    private String rdescr;

    private Date createDate;

    private String adminId;

    private String objID;//扩展第三方主键【】

    @ApiModelProperty(value="首页地址")
    private String indexUrl;


    public String getObjID() {
        return objID;
    }

    public void setObjID(String objID) {
        this.objID = objID;
    }

    public String getIndexUrl() {
        return indexUrl;
    }

    public void setIndexUrl(String indexUrl) {
        this.indexUrl = indexUrl;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getRname() {
        return rname;
    }

    public void setRname(String rname) {
        this.rname = rname == null ? null : rname.trim();
    }

    public String getRdescr() {
        return rdescr;
    }

    public void setRdescr(String rdescr) {
        this.rdescr = rdescr == null ? null : rdescr.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getAdminId() {
        return adminId;
    }

    public void setAdminId(String adminId) {
        this.adminId = adminId;
    }

    public String getRnameZh() {
        return rnameZh;
    }

    public void setRnameZh(String rnameZh) {
        this.rnameZh = rnameZh == null ? null : rnameZh.trim();
    }
}