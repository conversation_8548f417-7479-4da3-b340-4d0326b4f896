package com.jiuzhekan.cbkj.beans.sysExt;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class SysAdminInfoex implements Serializable {

    private String adminInfoexid;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "扩展类型：1输入码 2介绍 3协定方分享权限 4病历模板分享权限 5默认处方类型")
    private Integer userExType;

    @ApiModelProperty(value = "扩展编码：输入码(1拼音 2五笔)；权限(0私有 1科室 2医疗机构 3医共体)；处方类型(1内服 2外用 4适宜技术)")
    private String userExContent;

    @ApiModelProperty(value = "扩展文字: 权限(私有 科室 医疗机构 医共体)")
    private String userExText;

    private Date updateDate;

    public SysAdminInfoex(String userExContent, String userExText) {
        this.userExContent = userExContent;
        this.userExText = userExText;
    }

    public SysAdminInfoex(String adminInfoexid, String userId, Integer userExType, String userExContent, String userExText, Date updateDate) {
        this.adminInfoexid = adminInfoexid;
        this.userId = userId;
        this.userExType = userExType;
        this.userExContent = userExContent;
        this.userExText = userExText;
        this.updateDate = updateDate;
    }
}