package com.jiuzhekan.cbkj.beans.zkxc;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuzhekan.cbkj.controller.response.MedicalHistoryDetailResponse;
import com.jiuzhekan.cbkj.controller.response.SiZhenResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/16 11:21
 * @Version 1.0
 */
@Data
public class DiagnosisInfo {

    @ApiModelProperty(value = "预诊信息主键id")
    private Integer zkxcPadRecordId;
    @ApiModelProperty(value = "病历复合 0 未符合 1 复合")
    private Integer compositeMedical;

//    @ApiModelProperty(value = "中医四诊：-1. 未登记。待分配设备 0.已登记。 等待设备数据 1. 获取到设备数据")
//    private Integer infoStatus;
    @ApiModelProperty(value = "患者名字")
    private String patientName;

    @ApiModelProperty(value = "就诊卡号")
    private String medicalCardNo;

    @ApiModelProperty(value = "身份证件")
    private String patientCardNo;

    @ApiModelProperty(value = "（1上午 2下午）")
    private Byte checkRange;

    @ApiModelProperty(value = "性别")
    private String patientSex;

    @ApiModelProperty(value = "年龄")
    private String patientAge;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "预诊时间")
    private Date preDiagnosisTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "登记时间")
    private Date orderTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "报告时间")
    private Date reportTime;

    @ApiModelProperty(value = "主述")
    private String zhuShu;

    @ApiModelProperty(value = "现病史")
    private String xianBingShi;

    @ApiModelProperty(value = "既往史")
    private String jiWangShi;

    @ApiModelProperty(value = "体格检查")
    private String tiGeJianCha;

    @ApiModelProperty(value = "辅助检查")
    private String fuZhuJianCha;

    @ApiModelProperty(value = "治疗意见")
    private String zhiLiaoYijian;
    @ApiModelProperty(value = "体重")
    private String weight;

    @ApiModelProperty(value = "身高")
    private String height;



    @ApiModelProperty(value = "设备患者表的id")
    private Integer equipmentPatientId;

    @ApiModelProperty(value = "中医四诊")
    private SiZhenResponse siZhenResponse;
}
