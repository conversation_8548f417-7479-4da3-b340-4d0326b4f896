package com.jiuzhekan.cbkj.beans.bs.designateddelivery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/31 15:08
 * @Version 1.0
 */
@Data
@ApiModel
public class DesignatedDeliveryListRe {
    @ApiModelProperty(value = "乡镇代码")
    private String streetCode;
    @ApiModelProperty(value = "行政村代码")
    private String villageCode;
    @ApiModelProperty(value = "其它查询文本")
    private String keyword;


    @ApiModelProperty(value = "自提点类型")
    private String selfPickupTypeCode;
}
