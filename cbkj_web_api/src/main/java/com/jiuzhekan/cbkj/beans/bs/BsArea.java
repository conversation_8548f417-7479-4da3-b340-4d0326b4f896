package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel("区")
public class BsArea implements Serializable{

    private Integer areaId;

    @ApiModelProperty("市代码")
    private String cityCode;

    @ApiModelProperty("区代码")
    private String areaCode;

    @ApiModelProperty("区名称")
    private String areaName;

    @ApiModelProperty("简称")
    private String shortName;

    private String lng;

    private String lat;

    private Integer sort;

    private Date gmtCreate;

    private Date gmtModified;

    private String memo;

    private Integer dataState;


}
