package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TPrescriptionTechnicalAcu implements Serializable{

    @ApiModelProperty(value = "")
    private String technicalId;

    @ApiModelProperty(value = "穴位ID")
    private String acuId;

    @ApiModelProperty(value = "穴位代码")
    private String acuCode;

    @ApiModelProperty(value = "穴位名称")
    private String acuName;

    @ApiModelProperty(value = "针数")
    private String acuNum;

    @ApiModelProperty(value = "序号")
    private Short acuSeqn;


}
