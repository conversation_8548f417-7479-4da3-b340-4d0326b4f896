package com.jiuzhekan.cbkj.beans.business.doctor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel
public class TDoctorNote implements Serializable {

    @ApiModelProperty(value = "")
    private String noteId;

    @ApiModelProperty(value = "笔记模块:1名医验案 2中药 3方剂 4经络穴位 5疾病 6中成药 7舌诊 8脉象 9临床诊疗 10书籍")
    private Integer noteModule;

    @ApiModelProperty(value = "关联id:例如 验案id",required = true)
    private String connectId;

    @ApiModelProperty(value = "笔记类型数值:1 不分享,私有, 2 匿名共享,3实名共享,默认null.表中的值",required = true)
    private Integer noteType;

    @ApiModelProperty(value = "笔记类型字符串: 不分享,私有,  匿名共享,实名共享,默认null.用于页面展示")
    private String noteTypeStr;

    @ApiModelProperty(value = "笔记内容,默认null",required = true)
    private String noteDesc;

    @ApiModelProperty(value = "记笔记的人的ID")
    private String createUser;

    @ApiModelProperty(value = "记笔记的人的名称")
    private String noteUsername;

    @ApiModelProperty(value = "页面展示的时间字符串")
    private String dateStr;

    @ApiModelProperty(value = "笔记新增时默认收藏返回的收藏id")
    private String collectId;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private Date updateDate;

    @ApiModelProperty(value = "是否是自己的标志: 0 否 ,1是")
    private Integer isSelf = 0;
}