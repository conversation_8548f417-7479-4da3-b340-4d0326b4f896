package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TRecord2DicNew implements Serializable{

    @ApiModelProperty(value = "编号（保存用")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "必填性")
    private Object required;

    @ApiModelProperty(value = "默认勾选")
    private Object checked;

    @ApiModelProperty(value = "标签1，输入2，输入域3，数字输入4")
    private Integer type;

    @ApiModelProperty(value = "可收缩")
    private Boolean expandable ;

//    @ApiModelProperty(value = "可增加新的标签")
//    private Boolean appendable ;

    @ApiModelProperty(value = "后缀")
    private String suffix;

    @ApiModelProperty(value = "最小值")
    private Integer min;

    @ApiModelProperty(value = "最大值")
    private Integer max;

    @ApiModelProperty(value = "子数据")
    private List<TRecord2DicNew> children;

    @ApiModelProperty(value = "互斥id编号")
    private List<String> exclude;

    public TRecord2DicNew(String id, String name, Integer type) {
        this.id = id;
        this.name = name;
        this.type = type;
    }
}
