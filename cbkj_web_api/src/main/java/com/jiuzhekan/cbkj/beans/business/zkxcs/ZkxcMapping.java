package com.jiuzhekan.cbkj.beans.business.zkxcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcMapping implements Serializable{

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "")
    private String zkxcName;

    @ApiModelProperty(value = "")
    private String zkxcCode;

    @ApiModelProperty(value = "类型1.中科-映射一件事 2一件事-.辩证 3.辩证-（标准）中医电子病历 4 一件事-咳嗽病 5.一件事-痛风病6.一件事-胃脾病,7一件事-知识库图示（知识库的舌脉数据b_tongue_diagnosis和b_pulse_condition）")
    private Integer mapType;

    @ApiModelProperty(value = "")
    private String mapName;

    @ApiModelProperty(value = "")
    private String mapCode;

    @ApiModelProperty(value = "")
    private String mapOther;


}
