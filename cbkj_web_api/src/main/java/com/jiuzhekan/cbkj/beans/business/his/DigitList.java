package com.jiuzhekan.cbkj.beans.business.his;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DigitList {
    @ApiModelProperty(value = "患者登记主键id")
    private Integer equipmentPatientId;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "姓名")
    private String patientName;

    @ApiModelProperty(value = "0女1男")
    private String patientGender;

    @ApiModelProperty(value = "年龄")
    private Integer patientAge;

    @ApiModelProperty(value = "手机号")
    private String patientMobile;

    @ApiModelProperty(value = "证件号")
    private String patientCertificate;
    @ApiModelProperty(value = "挂号id(新华这个对接，挂号我们自己生成一个，防止多机构会重复)")
    private String registerId;

    //    @ApiModelProperty(value = "（新华）-1.待分配四诊仪器0.等待四诊仪数据 1.获取到四诊仪数据 2.已推送给HIS -2.推送失败")
//    private Integer infoStatus;
//    @ApiModelProperty(value = "推送失败原因")
//    private String failInfo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "登记日期")
    private Date orderTime;

    private String appId;
    private String insCode;
    @ApiModelProperty(value = "患者就诊卡号")
    private String medicalCardNo;


//    @ApiModelProperty(value = "登记日期")
//    private Date orderTime;
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @ApiModelProperty(value = "报告时间")
//    private Date reportTime;

    @ApiModelProperty(value = "查询条件记录下前端传过来的。1已登记2已完成",hidden = true)
    private Integer dataStatus;

    private List<EquipmentsListInfo> equipmentsListInfoList;
}
