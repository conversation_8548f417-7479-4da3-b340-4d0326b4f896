package com.jiuzhekan.cbkj.beans.http;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/11 15:51
 * @Version 1.0
 */
@ApiModel
@Data
public class DosageFormDicListReq {
    @ApiModelProperty(value = "搜索关键字")
    private String keyWord;

    @ApiModelProperty(value = "剂型id")
    private String itemId;

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "每页数量")
    private Integer limit;

    @ApiModelProperty(value = "字典来源类型")
    private Integer dicType;

//    @ApiModelProperty(value = "门诊住院标志")
    private String registerId;
}
