package com.jiuzhekan.cbkj.beans.business.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("药房配置配送信息表")
public class TDisplayExpress implements Serializable{

    @ApiModelProperty("配置ID")
    private String setId;

    @ApiModelProperty("机构药房配置ID")
    private String displayId;

    @ApiModelProperty("门诊，住院（1门诊，2住院）")
    private String outpatientOrHospitalization;

    @ApiModelProperty("配送是否开启（0是，1否）")
    private String showExpress;

    @ApiModelProperty("是否默认配送（0是，1否）")
    private String isExpress;

    @ApiModelProperty("是否启用配送地址（0是，1否）")
    private String isUseExpress;

    @ApiModelProperty(value = "0 开启定点配送 1 不启动定点配送")
    private String dcTypeSubclass;

    @ApiModelProperty("配送费用配置（0默认，1配置）")
    private String expressSet;


    /**
     * 药房配置费用明细
     */
    private List<TDisplayMoneySetting> settingList;

    @ApiModelProperty(value = "通用费用明细")
    private List<TDisplayMoneySetting> usuallyMoneySetting;


}
