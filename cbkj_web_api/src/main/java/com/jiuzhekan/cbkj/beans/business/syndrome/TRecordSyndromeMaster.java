package com.jiuzhekan.cbkj.beans.business.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @program: pre_api
 * @description: 国医大师和ai结果集
 * @author: wangtao
 * @create: 2021-03-25 10:57
 **/
@Data
public class TRecordSyndromeMaster  implements Serializable {
    private TRecordSyndromeGroup ai;
    private  TRecordMaster maestro;

    @ApiModelProperty(value = "舌象列表-V6.1.0新增")
    private List<TonguePulseSyndromeAsk> tongueList;
    @ApiModelProperty(value = "脉象列表-V6.1.0新增")
    private List<TonguePulseSyndromeAsk> pulseList;
}
