package com.jiuzhekan.cbkj.beans.drug;

import java.math.BigDecimal;

public class TCenterHisYpmlmxConvertUtil {

    public static TCenterHisYpmlmx transferMatBean(MatVo matVo, TCenterHisYpmlmx vo1) {
        vo1.setYpmlId(matVo.getDrugIdHis());
        vo1.setYaopindm(matVo.getMatPriceIdHis());
        vo1.setParentYpdm(matVo.getMatIdHis());
        vo1.setYaopinmc(matVo.getMatNameHis());
        vo1.setGuigeid(matVo.getMatSpeIdHis());
        vo1.setYaopingg(matVo.getMatSpeNameHis());
        vo1.setZhongyaolx(Byte.valueOf(matVo.getMatTypeHis()));
        vo1.setJiliang(null == matVo.getMatDoseHis() ? "" : matVo.getMatDoseHis().toString());
        vo1.setJiliangdw(matVo.getMatDoseUnitHis());
        vo1.setIsInsurance(matVo.getIsMedicalHis());
        vo1.setYaopinyf(matVo.getMatUsageHis());
        vo1.setUsageDesc(matVo.getUsageDescHis());
        vo1.setApprovalNumber(matVo.getApprovalNumberHis());
        vo1.setDailyMaxDoseExt(null == matVo.getDailyMaxDoseExtHis() ? null : Double.parseDouble(matVo.getDailyMaxDoseExtHis()));
        vo1.setDailyMaxDoseIn(null == matVo.getDailyMaxDoseInHis() ? null : Double.parseDouble(matVo.getDailyMaxDoseInHis()));
        vo1.setDailyMaxNumPrep(null == matVo.getDailyMaxNumPrepHis() ? null : Double.parseDouble(matVo.getDailyMaxNumPrepHis()));
        vo1.setLingshoujia(null == matVo.getSmallRetailPriceHis() ? null : new BigDecimal(matVo.getSmallRetailPriceHis()));

        vo1.setNotPayAlone(matVo.getNotPayAloneHis());
        vo1.setNotPayInFund(matVo.getNotPayInFundHis());
        vo1.setFrequencyId(matVo.getFrequencyIdHis());
        vo1.setFrequency(matVo.getFrequencyHis());
        vo1.setFrequencyRate(matVo.getFrequencyRateHis());
//vo1.setYpmlId(matVo.getDrugIdHis());
//vo1.setYaopindm(matVo.getMatPriceIdHis());
        vo1.setStockNum(matVo.getStockNum());
        vo1.setEffect(matVo.getEffect());
        vo1.setContent(matVo.getContent());
        vo1.setToxicityOverdoseMultiple(matVo.getToxicityOverdoseMultiple());
        vo1.setExternalUseOnly(matVo.getExternalUseOnly());
        vo1.setMaxdose(matVo.getMaxdose());
        vo1.setMindose(matVo.getMindose());


        return vo1;
    }
}
