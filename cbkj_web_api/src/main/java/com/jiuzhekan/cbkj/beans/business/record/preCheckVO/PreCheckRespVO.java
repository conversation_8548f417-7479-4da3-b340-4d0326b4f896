package com.jiuzhekan.cbkj.beans.business.record.preCheckVO;


import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionAcuItem;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionItem;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionPreparationItem;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "处方审核请求参数类")
public class PreCheckRespVO implements Serializable {
    @ApiModelProperty(value = "病历id")
    private String recId;
    @ApiModelProperty(value = "患者id")
    private String patientId;
    @ApiModelProperty(value = "患者姓名")
    private String recName;
    @ApiModelProperty(value = "医生ID")
    private String docId;
    @ApiModelProperty(value = "医生姓名")
    private String docName;
    @ApiModelProperty(value = "挂号id")
    private String registerId;
    @ApiModelProperty(value = "中医病名")
    private String disId;
    private String disName;
    @ApiModelProperty(value = "证型")
    private String symId;
    private String symName;
    @ApiModelProperty(value = "治法")
    private String theNames;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "申请时间")
    private Date recTreTime;
    @ApiModelProperty(value = "处方审核状态(-1 未审核 0未通过 1待支付 2已支付)")
    private String preStatus;
    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    // 1204 扩展字段
    @ApiModelProperty(value = "处方明细凭拼接")
    private String matNames;
    @ApiModelProperty(value = "处方类型")
    private String preType;
    @ApiModelProperty(value = "药品剂型（1散装饮片  2散装颗粒  4小包装饮片 5小包装颗粒  6膏方）")
    private String preMatType;
    @ApiModelProperty(value = "药房ID")
    private String storeId;
    @ApiModelProperty(value = "处方ID")
    private String preId;
    @ApiModelProperty(value = "处方号")
    private String preNo;
    @ApiModelProperty(value = "病人就诊卡号")
    private String cardNo;
    @ApiModelProperty(value = "处方作废标志")
    private String isDel;
    @ApiModelProperty(value = "贴数（草药为贴数，熏蒸为总次数，适宜技术为天数）")
    private String preNum;
    @ApiModelProperty(value = "来源处方名")
    private String preOriginName;
    @ApiModelProperty(value = "处方状态 ")
    private String recType;
    private String recTypeStr;
    @ApiModelProperty(value = "his病历号 ")
    private String visitNo;
    @ApiModelProperty(value = "处方药品明细集合", required = true)
    private List<TPrescriptionItem> itemList;
    @ApiModelProperty(value = "处方穴位明细集合", required = true)
    private List<TPrescriptionAcuItem> acuItemList;
    @ApiModelProperty(value = "处方明细集合", required = true)
    private TPrescription preDetailItems;
    @ApiModelProperty(value = "中药制剂明细集合", required = true)
    private List<TPrescriptionPreparationItem> preparationItemList;

    // 0203 扩展字段
    @ApiModelProperty(value = "年龄1")
    private String recAge1;
    @ApiModelProperty(value = "年龄2")
    private String recAge2;
    @ApiModelProperty(value = "年龄单位1")
    private String recAgeunit1;
    @ApiModelProperty(value = "年龄单位2")
    private String recAgeunit2;
    @ApiModelProperty(value = "患者性别")
    private String recGender;

    @TianAnDecryptField
    @ApiModelProperty(value = "患者住址")
    private String recAddress;

    @TianAnDecryptField
    @ApiModelProperty(value = "患者电话")
    private String patientMobile;

    @ApiModelProperty(value = "就诊科室名称")
    private String deptName;
    @ApiModelProperty(value = "是否医保（1医保 0自费）")
    private String costType;
    @ApiModelProperty(value = "审核医生姓名")
    private String checkUsername;
    @ApiModelProperty(value = "医生姓名")
    private String preDoctorname;
    @ApiModelProperty(value = "处方总价")
    private String preTolMoney;
    @ApiModelProperty(value = "药品总价")
    private String matTolMoney;
    @ApiModelProperty(value = "医嘱")
    private String preAdvice;
    @ApiModelProperty(value = "是否膏方（1是 0否）")
    private String isProduction;
    @ApiModelProperty(value = "煎药方式（1为代煎，0为自煎）")
    private String decoctType;
    @ApiModelProperty(value = "代煎贴数")
    private String preDecoctNum;
    @ApiModelProperty(value = "取药方式（1配送，0自提）")
    private String dcType;
    @ApiModelProperty(value = "是否院内治疗（1是 0否）")
    private String isHospitalTreatment;
    @ApiModelProperty(value = "外用方式")
    private String preSmokeType;
    @ApiModelProperty(value = "熏蒸仪")
    private String preSmokeInstrument;
    @ApiModelProperty(value = "服法")
    private String preDescription;
    @ApiModelProperty(value = "频次")
    private String preUsetimeDes;
    @ApiModelProperty(value = "服药时间")
    private String preFrequency;
    @ApiModelProperty("患者类型(0自费1普通医保2医保离职3职工特慢J居民特慢)")
    private String patientTypes;
    @ApiModelProperty(value = "门诊住院标志（1门诊 2住院）")
    private Integer recTreType;
}