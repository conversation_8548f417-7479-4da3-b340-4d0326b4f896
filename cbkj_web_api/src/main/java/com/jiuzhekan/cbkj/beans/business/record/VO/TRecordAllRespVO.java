package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.common.utils.PublicVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "所有大病历响应类")
@Data
public class TRecordAllRespVO  implements Serializable {
    @ApiModelProperty(value = "所有大病历集合,一条数据就是一个大病历,按照诊次升序排列")
    private List<TBigRecordRespVO> allBigRec;
 }