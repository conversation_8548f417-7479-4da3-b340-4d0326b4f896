package com.jiuzhekan.cbkj.beans.statistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by zbh on 2024/4/19 17:23
 *
 * @description：
 */
@Setter
@Getter
public class InstitutionStatsVO implements Serializable {
    private String appId;

    private String insCode;
    private String insName;
    private String regCount;
    private String nfCount;
    private String wyCount;
    private String zcCount;
    private String syCount;

    @JsonProperty("pCount")
    private String pCount;
    private String qregCount;
    private String qnfCount;
    private String qwyCount;
    private String qzcCount;
    private String qsyCount;
    private String qpCount;
}
