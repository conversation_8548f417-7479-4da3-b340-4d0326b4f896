package com.jiuzhekan.cbkj.beans.business.his;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DigitPadList {
    @ApiModelProperty(value = "患者登记主键id")
    private Integer equipmentPatientId;

    @ApiModelProperty(value = "患者id")
    private String patientId;
    private String visitNo;

    @ApiModelProperty(value = "姓名")
    private String patientName;

    @ApiModelProperty(value = "0女1男")
    private String patientGender;

    @ApiModelProperty(value = "年龄")
    private Integer patientAge;

    @ApiModelProperty(value = "手机号")
    private String patientMobile;

    @ApiModelProperty(value = "证件号")
    private String patientCertificate;
    @ApiModelProperty(value = "挂号id(新华这个对接，挂号我们自己生成一个，防止多机构会重复)")
    private String registerId;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "登记日期")
    private Date orderTime;


    @ApiModelProperty(value = "患者就诊卡号")
    private String medicalCardNo;
    /**
     * 下面字段新华PAd需求
     */
    @ApiModelProperty(value = "体格状态 0 未完成 1完成")
    private Integer physiqueStatus;

    @ApiModelProperty(value = "预问诊 0 未完成 1完成")
    private Integer diagnosisStatus;

    @ApiModelProperty(value = "病历复合 0 未符合 1 复合")
    private Integer compositeMedical;

    @ApiModelProperty(value = "-1.待分配设备 0.等待设备数据 1.获取到设备数据")
    private Integer infoStatus;

//    @ApiModelProperty(value = "0.候诊中 1. 已完成")
//    private  Integer dataStatus;
    private  String opClassName;
    private  String regNum;


}
