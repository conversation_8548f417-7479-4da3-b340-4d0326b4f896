package com.jiuzhekan.cbkj.beans.statistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsFunction implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "功能项")
    private String functionName;

    @ApiModelProperty(value = "功能来源")
    private String functionSource;

    @ApiModelProperty(value = "使用次数")
    private BigDecimal usageTimes;

    @ApiModelProperty(value = "后缀")
    private String suffix;

    @ApiModelProperty(value = "是否展示图表")
    private boolean showChart;

    @ApiModelProperty(value = "统计日期")
    private String createDate;

    @ApiModelProperty(value = "开始日期")
    private String beginDate;

    @ApiModelProperty(value = "结束日期")
    private String endDate;
    @ApiModelProperty(value = "开始日期")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    private Date endTime;

    public TStatisticsFunction(String functionName, String functionSource) {
        this.functionName = functionName;
        this.functionSource = functionSource;
    }

    public TStatisticsFunction(String functionName, String beginDate, String endDate) {
        this.functionName = functionName;
        this.beginDate = beginDate;
        this.endDate = endDate;
    }

    public TStatisticsFunction(String functionName, String functionSource, String beginDate, String endDate) {
        this.functionName = functionName;
        this.functionSource = functionSource;
        this.beginDate = beginDate;
        this.endDate = endDate;
    }
}
