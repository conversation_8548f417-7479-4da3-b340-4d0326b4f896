package com.jiuzhekan.cbkj.beans.business.doctor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDoctorBook implements Serializable {

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "医生ID")
    private String doctorId;

    @ApiModelProperty(value = "医生姓名")
    private String doctorName;

    @ApiModelProperty(value = "书籍ID")
    private String bookId;

    @ApiModelProperty(value = "书籍名称")
    private String bookName;

    @ApiModelProperty(value = "作者")
    private String author;

    @ApiModelProperty(value = "章节ID")
    private String chapterId;

    @ApiModelProperty(value = "阅读进度")
    private String progress;

    @ApiModelProperty(value = "已读比例")
    private BigDecimal rate;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "加入书架 1是0否")
    private Integer shelf;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "阅读时间")
    private String readTime;

    @ApiModelProperty(value = "知识库书籍详情")
    private Object bookDetail;

    public TDoctorBook(String doctorId, String bookId) {
        this.doctorId = doctorId;
        this.bookId = bookId;
    }
}
