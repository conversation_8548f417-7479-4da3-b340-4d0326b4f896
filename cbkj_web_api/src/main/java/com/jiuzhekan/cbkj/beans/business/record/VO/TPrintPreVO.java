package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.beans.business.record.TOrderStatus;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionAcuItem;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionItem;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionPreparationItem;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "处方表")
public class TPrintPreVO implements Serializable {
    @ApiModelProperty(value = "处方ID")
    private String preId;
    @ApiModelProperty(value = "处方号码")
    private String preNo;

    @ApiModelProperty(value = "开方医生姓名")
    private String preDoctorname;
    @ApiModelProperty(value = "开方时间")
    private Date preTime;
    @ApiModelProperty(value = "单剂金额")
    private String preSingleMoney;
    @ApiModelProperty(value = "贴数（草药为贴数，熏蒸为总次数，适宜技术为天数）")
    private Short preNum;
    @ApiModelProperty(value = "门诊住院标志（1门诊 2住院）")
    private Short preMzZy;
    @ApiModelProperty(value = "处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方）")
    private String preType;
    @ApiModelProperty(value = "药品剂型（1散装饮片  2散装颗粒  4小包装饮片 5小包装颗粒  6膏方）")
    private String preMatType;
    @ApiModelProperty(value = "用法")
    private String preUsage;
    @ApiModelProperty(value = "服法")
    private String preDescription;
    @ApiModelProperty(value = "频次")
    private String preFrequency;
    @ApiModelProperty(value = "医嘱")
    private String preAdvice;
    @ApiModelProperty(value = "药品总金额")
    private String matTolMoney;
    @ApiModelProperty(value = "处方总金额")
    private String preTolMoney;
    @ApiModelProperty(value = "处方实收总金额")
    private String preTolMoneyReal;
    @ApiModelProperty(value = "取药方式（1配送，0自提）")
    private String dcType;
    @ApiModelProperty(value = "煎药方式（1为代煎，0为自煎）")
    private String decoctType;
    @ApiModelProperty(value = "是否膏方（0否 1是）")
    private String isProduction;

    @ApiModelProperty(value = "代煎费")
    private String preDecoctionFee;
    @ApiModelProperty(value = "制膏费")
    private String preProductionFee;
    @ApiModelProperty(value = "配送费")
    private String preExpressFee;
    @ApiModelProperty(value = "治疗费")
    private String preSmoMoney;
    @ApiModelProperty(value = "特殊调配费")
    private BigDecimal preSpecialFee;

    @ApiModelProperty(value = "适宜技术类型代码（1针灸，2艾灸，3推拿，4拔罐）",position = 0)
    private String acuType;
    @ApiModelProperty(value = "针灸项目（1银针 2电针）",position = 1)
    private String acuProject;
    @ApiModelProperty(value = "操作指南")
    private String acuOperation;

    @ApiModelProperty(value = "服药时间说明")
    private String preUsetimeDes;
    @ApiModelProperty(value = "每次几ml")
    private String preNMl;
    @ApiModelProperty(value = "每次几ml")
    private String preNMlName;
    @ApiModelProperty(value = "每次几袋")
    private String preNBag;
    @ApiModelProperty(value = "熏蒸仪（A类 B类）")
    private String preSmokeInstrument;
    @ApiModelProperty(value = "是否院内治疗（1是 0否）")
    private String isHospitalTreatment;


    /**
     * @Description : 以下非t_prescripion表的数据
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/23 20:26
     */
    @ApiModelProperty(value = "发药人姓名(药房处方表)")
    private String sendUsername;
    @ApiModelProperty(value = "配药人姓名(药房处方表)")
    private String dosageUsername;
    @ApiModelProperty(value = "取药人姓名(药房处方表)")
    private String getUsername;
    @ApiModelProperty(value = "复核人姓名(药房处方表)")
    private String reviewUsername;
    @ApiModelProperty(value = "收货人(配送表)")
    private String dcName;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人手机号(配送表)")
    private String dcMobile;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人详细地址(配送表)")
    private String dcAddress;

    @TianAnDecryptField
    @ApiModelProperty(value = "省(配送表)")
    private String dcCounty;

    @TianAnDecryptField
    @ApiModelProperty(value = "市(配送表)")
    private String dcTown;

    @TianAnDecryptField
    @ApiModelProperty(value = "区(配送表)")
    private String dcVillage;

    @TianAnDecryptField
    @ApiModelProperty(value = "街道(配送表)")
    private String dcStreet;

    @ApiModelProperty(value = "省代码(配送表)")
    private String dcCountyCode;
    @ApiModelProperty(value = "市代码(配送表)")
    private String dcTownCode;
    @ApiModelProperty(value = "区代码(配送表)")
    private String dcVillageCode;
    @ApiModelProperty(value = "街道代码(配送表)")
    private String dcStreetCode;
    @ApiModelProperty(value = "快递公司(配送表)")
    private String dcCompany;
    @ApiModelProperty(value = "快递单号(配送表)")
    private String dcNumber;
    @ApiModelProperty(value = "是否发货（0未发货 1已发货）(配送表)")
    private String isDeliver;

    @ApiModelProperty(value = "草药处方明细集合(内服,外用,中成药)(处方测评表)")
    private List<TPrescriptionItem> itemList;

    /**
     * 适宜技术处方明细表
     */
    @ApiModelProperty(value = "适宜技术处方明细集合")
    private List<TPrescriptionAcuItem> acuItemList;

    @ApiModelProperty(value = "订单状态集合")
    private List<TOrderStatus> orderList;

    @ApiModelProperty(value = "中药制剂明细集合", required = true)
    private List<TPrescriptionPreparationItem> preparationItemList;

    /**
     * 处方状态表
     */
    /* private List<TPrescriptionStatus> statusList;*/
}