package com.jiuzhekan.cbkj.beans.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 14:15
 * @Version 1.0
 */
@Data
@ApiModel
public class MZHistoryDetail {
    private Date insertTime;
    @ApiModelProperty(value = "左手和右手寸关尺信息的主键id：顺序是：左手寸关尺，右手寸关尺")
    private String[] zkxcChiCunGuanIds;
    private String[] mzResultType;
//    @ApiModelProperty(value = "左手-尺")
//    private String leftChiPulsePotential;
//    @ApiModelProperty(value = "左手-寸")
//    private String leftCunPulsePotential;
//    @ApiModelProperty(value = "左手-关")
//    private String leftGuanPulsePotential;
//    @ApiModelProperty(value = "右手-尺")
//    private String rightChiPulsePotential;
//
//    @ApiModelProperty(value = "右手-寸")
//    private String rightCunPulsePotential;
//
//    @ApiModelProperty(value = "右手-关")
//    private String rightGuanPulsePotential;

    @ApiModelProperty(value = "寸关尺的脉象：顺序是：左手寸关尺，右手寸关尺")
    private String[] pulsePotentials;
    @ApiModelProperty(value = "脉位描述：顺序是：左手寸关尺，右手寸关尺")
    private String[] maiWei;

    public void setMaiWei(String[] maiWei){
        for (int i = 0; i < maiWei.length; i++) {
            if (Double.parseDouble(maiWei[i])  > maiWeiMin && Double.parseDouble(maiWei[i]) < maiWeiMax ){
                maiWei[i] = "正常";
                continue;
            }
            if ( Double.parseDouble(maiWei[i]) > maiWeiMax ){
                maiWei[i] = "偏浮";
                continue;
            }
            if ( Double.parseDouble(maiWei[i]) < maiWeiMin ){
                maiWei[i] = "偏沉";
                continue;
            }
        }
        this.maiWei = maiWei;
    }
    private Double maiWeiMin = 200D;
    private Double maiWeiMax = 350D;
    @ApiModelProperty(value = "脉率描述：顺序是：左手寸关尺，右手寸关尺")
    private String[] maiLv;

    public void setMaiLv(String[] maiLv){
        for (int i = 0; i < maiLv.length; i++) {
            if (Double.parseDouble(maiLv[i])  > maiLvMin && Double.parseDouble(maiLv[i]) < maiLvMax ){
                maiLv[i] = "正常";
                continue;
            }
            if ( Double.parseDouble(maiLv[i]) > maiLvMax ){
                maiLv[i] = "偏快";
                continue;
            }
            if ( Double.parseDouble(maiLv[i]) < maiLvMin ){
                maiLv[i] = "偏慢";
                continue;
            }
        }
        this.maiLv = maiLv;
    }



    private Double maiLvMin = 60D;
    private Double maiLvMax = 100D;
    @ApiModelProperty(value = "节率描述：顺序是：左手寸关尺，右手寸关尺")
    private String[] jieLv;

    public void setJieLv(String[] jieLv){
        for (int i = 0; i < jieLv.length; i++) {
            if (Double.parseDouble(jieLv[i])  > jieLvMin && Double.parseDouble(jieLv[i]) < jieLvMax ){
                jieLv[i] = "正常";
                continue;
            }
            if ( Double.parseDouble(jieLv[i]) > jieLvMax ){
                jieLv[i] = "偏快";
                continue;
            }
            if ( Double.parseDouble(jieLv[i]) < jieLvMin ){
                jieLv[i] = "偏慢";
                continue;
            }
        }
        this.jieLv = jieLv;
    }


    private Double jieLvMin = 0D;
    private Double jieLvMax = 0.15D;
    @ApiModelProperty(value = "脉力描述：顺序是：左手寸关尺，右手寸关尺")
    private String[] maiLi;


    public void setMaiLi(String[] maiLi){
        for (int i = 0; i < maiLi.length; i++) {
            if (Double.parseDouble(maiLi[i])  > maiLiMin && Double.parseDouble(maiLi[i]) < maiLiMax ){
                maiLi[i] = "正常";
                continue;
            }
            if ( Double.parseDouble(maiLi[i]) > maiLiMax ){
                maiLi[i] = "偏快";
                continue;
            }
            if ( Double.parseDouble(maiLi[i]) < maiLiMin ){
                maiLi[i] = "偏慢";
                continue;
            }
        }
        this.maiLi = maiLi;
    }


    private Double maiLiMin = 16D;
    private Double maiLiMax = 25D;
    @ApiModelProperty(value = "紧张度描述：顺序是：左手寸关尺，右手寸关尺")
    private String[] jinZhangDu;



    public void setJinZhangDu(String[] jinZhangDu){
        for (int i = 0; i < jinZhangDu.length; i++) {

            if ( Double.parseDouble(jinZhangDu[i]) > jinZhangDuMax ){
                jinZhangDu[i] = "偏弦";
            }
            else if ( Double.parseDouble(jinZhangDu[i]) < jinZhangDuMax ){
                jinZhangDu[i] = "适中";
            }
        }
        this.jinZhangDu = jinZhangDu;
    }

    private Double jinZhangDuMax = 0.7D;

    @ApiModelProperty(value = "流利度描述：顺序是：左手寸关尺，右手寸关尺")
    private String[] liuLiDu;


    public void setLiuLiDu(String[] liuLiDu){
        for (int i = 0; i < liuLiDu.length; i++) {

            if ( Double.parseDouble(liuLiDu[i]) > liuLiDuMax ){
                liuLiDu[i] = "偏弦";
            }
            else if ( Double.parseDouble(liuLiDu[i]) < liuLiDuMax ){
                liuLiDu[i] = "适中";
            }
        }
        this.liuLiDu = liuLiDu;
    }
    private Double liuLiDuMax = 0.6D;



    @ApiModelProperty(value = "整体描述")
    private String totalPulseFeatureDesc;
    @ApiModelProperty(value = "波动图")
    private String pulseQuantificationImg;
}
