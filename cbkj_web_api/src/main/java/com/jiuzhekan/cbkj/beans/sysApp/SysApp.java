package com.jiuzhekan.cbkj.beans.sysApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("医联体")
public class SysApp implements Serializable {

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医联体名称")
    private String appName;

    @ApiModelProperty("医联体密码")
    private String appPwd;

    @ApiModelProperty("医联体描述")
    private String appDes;

    @ApiModelProperty(hidden = true)
    private Date createDate;

    @ApiModelProperty(hidden = true)
    private String createUser;

    @ApiModelProperty(hidden = true)
    private String createUsername;

    @ApiModelProperty(hidden = true)
    private Date updateDate;

    @ApiModelProperty(hidden = true)
    private String updateUser;

    @ApiModelProperty(hidden = true)
    private String updateUsername;

    @ApiModelProperty(hidden = true)
    private Date delDate;

    @ApiModelProperty(hidden = true)
    private String delUser;

    @ApiModelProperty(hidden = true)
    private String delUsername;

    @ApiModelProperty(hidden = true)
    private String isDel;

    @ApiModelProperty(hidden = true)
    private Date disableDate;

    @ApiModelProperty(hidden = true)
    private String disableUser;

    @ApiModelProperty(hidden = true)
    private String disableUsername;

    @ApiModelProperty("是否禁用 1：禁用 0：启用")
    private String isDisable;

    private List<SysInstitution> insList;

    private List<Map<String, Object>> appInsList;
}