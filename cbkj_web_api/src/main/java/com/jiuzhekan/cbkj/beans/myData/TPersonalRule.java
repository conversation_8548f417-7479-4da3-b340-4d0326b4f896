package com.jiuzhekan.cbkj.beans.myData;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TPersonalRule implements Serializable{

    @ApiModelProperty(value = "协定方角色ID")
    private String ruleId;

    @ApiModelProperty(value = "角色名称")
    private String ruleName;

    @ApiModelProperty(value = "角色描述")
    private String ruleDesc;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;


}
