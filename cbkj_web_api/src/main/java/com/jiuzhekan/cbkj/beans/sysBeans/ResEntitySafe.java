package com.jiuzhekan.cbkj.beans.sysBeans;

import com.jiuzhekan.cbkj.beans.validRecordBySDK.EvaluationResponse;
import com.jiuzhekan.cbkj.common.utils.Constant;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应实体
 */
@Data
public class ResEntitySafe implements Serializable {

    private static final long serialVersionUID = -1439573222637546375L;
    private boolean status = true;
    private int code = 0;
    private String message;
    private EvaluationResponse data;

}

