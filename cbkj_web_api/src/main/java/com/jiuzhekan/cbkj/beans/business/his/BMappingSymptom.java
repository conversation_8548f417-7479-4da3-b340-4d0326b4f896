package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 证型映射
 *
 * <AUTHOR>
 * @date 2020/11/21
 */
@Data
@NoArgsConstructor
@ApiModel
public class BMappingSymptom implements Serializable {

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "证型ID-HIS")
    private String symIdHis;

    @ApiModelProperty(value = "证型ID-SYS")
    private String symIdSys;

    @ApiModelProperty(value = "证型编码-HIS")
    private String symCodeHis;

    @ApiModelProperty(value = "证型编码-SYS")
    private String symCodeSys;

    @ApiModelProperty(value = "证型名称-HIS")
    private String symNameHis;

    @ApiModelProperty(value = "证型名称-SYS")
    private String symNameSys;




}
