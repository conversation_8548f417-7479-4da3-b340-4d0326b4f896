package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TRecord2Smell implements Serializable{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "病历编号")
    private String recId;

    @ApiModelProperty(value = "标签编号")
    private String tagNo;

    @ApiModelProperty(value = "声音")
    private String wztsy;
    private String wztsydm;

    @ApiModelProperty(value = "声音其他")
    private String wztsyqt;

    @ApiModelProperty(value = "嗅气味")
    private String wzxqw;
    private String wzxqwdm;

    @ApiModelProperty(value = "嗅气味其他")
    private String wzxqwqt;

    @ApiModelProperty(value = "其他")
    private String wzqt2;

    @ApiModelProperty(value = "状态【0:可用，1:禁用】")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    private Integer equipmentPatientId;


}
