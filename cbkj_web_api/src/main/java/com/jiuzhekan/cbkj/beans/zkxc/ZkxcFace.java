package com.jiuzhekan.cbkj.beans.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcFace implements Serializable{

    @ApiModelProperty(value = "面诊自增id")
    private Integer faceId;

    @ApiModelProperty(value = "脸颊特征")
    private String area;

    @ApiModelProperty(value = "脸颊特征解析")
    private String areaDescription;

    @ApiModelProperty(value = "面色特征")
    private String color;

    @ApiModelProperty(value = "面色特征")
    private String colorDescription;

    @ApiModelProperty(value = "面色光泽")
    private String light;

    @ApiModelProperty(value = "面色光泽解析")
    private String lightDescription;

    @ApiModelProperty(value = "唇色")
    private String lip;

    @ApiModelProperty(value = "唇色 解析")
    private String lipDescription;

    @ApiModelProperty(value = "面原图 base64")
    private String faceOriginalImg;
    private Integer collectId;
    private String registerId;


}
