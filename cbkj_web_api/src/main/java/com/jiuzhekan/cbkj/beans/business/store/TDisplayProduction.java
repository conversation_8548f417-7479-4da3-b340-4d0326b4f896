package com.jiuzhekan.cbkj.beans.business.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("药房配置制膏信息表")
public class TDisplayProduction implements Serializable{

    @ApiModelProperty("配置ID")
    private String setId;

    @ApiModelProperty("机构药房配置ID")
    private String displayId;

    @ApiModelProperty("门诊，住院(1门诊，2住院)")
    private String outpatientOrHospitalization;

    @ApiModelProperty("制膏配置是否开启(0是，1否)")
    private String showProduction;

    @ApiModelProperty("医保患者是否显示膏方(0是，1否)")
    private String isShowProduction;

    @ApiModelProperty("是否显示膏方类型(0是，1否)")
    private String isShowProductionType;

    @ApiModelProperty("膏方控制开方贴数")
    private Integer productionNum;

    @ApiModelProperty("通用制膏费用配置（0默认，1配置）")
    private String usuallyProductionSet;

    @ApiModelProperty("配方制膏费用配置（0默认，1配置）")
    private String formulaProductionSet;

    @ApiModelProperty("默认制膏（1.关，0开）")
    private String isProduction;


    /**
     * 药房配置费用明细
     */
    private List<TDisplayMoneySetting> settingList;

    @ApiModelProperty(value = "通用费用明细")
    private List<TDisplayMoneySetting> usuallyMoneySetting;

    @ApiModelProperty(value = "配送费用明细")
    private List<TDisplayMoneySetting> formulaMoneySetting;

}
