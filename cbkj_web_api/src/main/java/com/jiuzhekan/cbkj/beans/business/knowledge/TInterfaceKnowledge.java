package com.jiuzhekan.cbkj.beans.business.knowledge;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
public class TInterfaceKnowledge implements Serializable {

    private String id;

    /**
     * 知识库的appId
     */
    private String appId;
    /**
     * 知识库的密码
     */
    private String appPwd;
    /**
     * 知识库的地址
     */
    private String url;
    /**
     * 知识库的token
     */
    private String token;

    private Date effectiveTime;
}