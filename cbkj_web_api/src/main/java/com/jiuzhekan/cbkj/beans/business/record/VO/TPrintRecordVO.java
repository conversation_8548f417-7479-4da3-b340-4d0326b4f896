package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.beans.business.record.TRecordDetail;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "预览打印处方接收到实体类")
@Data
public class TPrintRecordVO implements Serializable {
    @ApiModelProperty(value = "病历ID")
    private String recId;
    @ApiModelProperty(value = "姓名")
    private String recName;
    @ApiModelProperty(value = "年龄1")
    private Short recAge1;
    @ApiModelProperty(value = "年龄单位1")
    private String recAgeunit1;
    @ApiModelProperty(value = "年龄2")
    private Short recAge2;
    @ApiModelProperty(value = "年龄单位2")
    private String recAgeunit2;
    @ApiModelProperty(value = "性别")
    private String recGender;
    @ApiModelProperty(value = "住址")
    private String recAddress;
    @ApiModelProperty(value = "就诊时间")
    private Date recTreTime;
    @ApiModelProperty(value = "就诊科室名称")
    private String deptName;
    @ApiModelProperty(value = "病历类型（分两类：门诊、住院医嘱，对应值分别为1、2）")
    private String recTreType;
    @ApiModelProperty(value = "西医诊断结果")
    private String westernDisease;
    @ApiModelProperty(value = "疾病名称")
    private String disName;
    @ApiModelProperty(value = "证型名称")
    private String symName;
    @ApiModelProperty(value = "治法字符串")
    private String theNames;

    // 挂号表信息
    @ApiModelProperty(value = "就诊医生姓名( 挂号表)")
    private String doctorName;
    @ApiModelProperty(value = "就诊号( 挂号表)")
    private String registerNum;

    @TianAnDecryptField
    @ApiModelProperty(value = "患者手机号(患者表)")
    private String patientMobile;

    @ApiModelProperty(value = "就诊卡号(患者表)")
    private String medicalCardNo;

    @ApiModelProperty(value = "收货人(配送表,遍历处方,哪个方子存在配送数据就显示到这里)")
    private String dcName;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人手机号(配送表)")
    private String dcMobile;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人地址(配送表)")
    private String dcAddress;

    @ApiModelProperty(value = "是否代煎 1是 0 否")
    private String decoctType;
    @ApiModelProperty(value = "是否膏方 1是 0 否")
    private String isProduction;

    // 病历从表
    @ApiModelProperty(value = "病历从表集合")
    private List<TRecordDetail> recordDetailList;

    @ApiModelProperty(value = "处方表集合")
    private List<TPrintPreVO> preList;   //处方表
 }