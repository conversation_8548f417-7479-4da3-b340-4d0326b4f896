package com.jiuzhekan.cbkj.beans.business.sysCode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "系统代码明细")
public class TSysCodeItem implements Serializable {

    private String itemId;

    @ApiModelProperty(value = "代码明细名称")
    private String itemName;
    @ApiModelProperty(value = "大类id")
    private Short codeId;
    @ApiModelProperty(value = "大类名称")
    private String codeName;

    @ApiModelProperty(value = "隐藏子项")
    private String hideProject;

    @ApiModelProperty(value = "=显示子项")
    private String zifu1;

    private String zifu2;

    private String zifu3;

    private String zifu4;

    @ApiModelProperty(value = "是否默认 1是 0 否")
    private String zifu5;

    private Date createDate;

    private String createUser;

    private String createUsername;

    private Date updateDate;

    private String updateUser;

    private String updateUsername;

    private Date delDate;

    private String delUser;

    private String delUsername;
    @ApiModelProperty(value = "是否删除（0否 1是）")
    private String isDel;

    @ApiModelProperty(value = "序号")
    private Short itemNum;

    @ApiModelProperty(value = "系数（频次：次/天）")
    private Short rate;
}