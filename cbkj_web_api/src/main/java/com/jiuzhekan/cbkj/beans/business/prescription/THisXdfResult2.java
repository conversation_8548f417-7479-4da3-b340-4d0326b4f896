package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class THisXdfResult2 implements Serializable{

    @ApiModelProperty(value = "HIS-协定方序号")
    private String xh;
    @ApiModelProperty(value = "协定方名称")
    private String preName;
    @ApiModelProperty(value = "协定方ID")
    private String persPreId;
    @ApiModelProperty(value = "协定方类型")
    private String preType;


}
