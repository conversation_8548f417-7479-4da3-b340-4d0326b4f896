package com.jiuzhekan.cbkj.beans.referral;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/27 14:48
 * @Version 1.0
 */
public enum MaritalStatus {
    // 未婚
    UNMARRIED("10", "未婚"),
    // 已婚（这里我们使用一个通用的已婚状态，但实际情况可能需要更具体的分类）
    MARRIED("20", "已婚"),
    // 初婚
    FIRST_MARRIAGE("21", "初婚"),
    // 再婚
    REMARRIED("22", "再婚"),
    // 复婚
    RESTORED_MARRIAGE("23", "复婚"),
    // 丧偶
    WIDOWED("30", "丧偶"),
    // 离婚
    DIVORCED("40", "离婚"),
    // 未说明的婚姻状况
    UNSPECIFIED("90", "未说明的婚姻状况");

    // 成员变量，存储婚姻状况的代码和名称
    private final String code;
    private final String name;

    // 枚举的构造方法必须是私有的
    MaritalStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // 获取婚姻状况的代码
    public String getCode() {
        return code;
    }

    // 获取婚姻状况的名称
    public String getName() {
        return name;
    }

    // 可以通过代码字符串来查找对应的枚举实例
    public static MaritalStatus fromCode(String code) {
        for (MaritalStatus status : MaritalStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        // 如果没有找到对应的枚举值，则返回null或者抛出异常，具体根据需要
        // 这里我们返回null
        return null;
    }

    // 可以通过名称来查找对应的枚举实例（可选）
    public static MaritalStatus fromName(String name) {
        for (MaritalStatus status : MaritalStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        // 如果没有找到对应的枚举值，则返回null或者抛出异常，具体根据需要
        // 这里我们返回null
        return null;
    }

    public static LinkedHashMap<String, String> getAllEthnicitiesAsMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        for (MaritalStatus ethnicity : MaritalStatus.values()) {
            map.put(ethnicity.getCode(), ethnicity.getName());
        }
        LinkedHashMap<String, String> sortedMap = map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(String::compareTo)) // 这里使用了默认的字符串比较
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, // 合并函数，这里其实不会用到，因为每个键都是唯一的
                        LinkedHashMap::new // 使用 LinkedHashMap 来保持排序后的顺序
                ));

        return sortedMap;

    }
}
