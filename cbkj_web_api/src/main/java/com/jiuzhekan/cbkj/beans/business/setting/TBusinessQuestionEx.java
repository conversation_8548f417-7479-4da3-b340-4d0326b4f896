package com.jiuzhekan.cbkj.beans.business.setting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "常见问题扩展类" ,description = "常见问题扩展类(用于记录点赞)")
public class TBusinessQuestionEx implements Serializable {
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "问题id")
    private String questionId;
    @ApiModelProperty(value = "是否有帮助  (0是,1否)")
    private String helpful;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改时间  (** 即点赞时间)")
    private Date updateTime;
    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;
    @ApiModelProperty(value = "是否删除(0 否 ,1是)")
    private String isDel;
}