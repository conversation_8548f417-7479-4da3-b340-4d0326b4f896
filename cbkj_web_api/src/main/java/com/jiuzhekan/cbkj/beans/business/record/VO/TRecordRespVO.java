package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.beans.business.record.TRecordDetail;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
@ApiModel(value = "单份病历响应类")
@Data
public class TRecordRespVO  implements Serializable {
    @ApiModelProperty(value = "病历ID")
    private String recId;
    @ApiModelProperty(value = "挂号ID（时间戳）")
    private String registerId;
    @ApiModelProperty(value = "患者ID")
    private String patientId;
    @ApiModelProperty(value = "姓名")
    private String recName;
    @ApiModelProperty(value = "年龄1")
    private Short recAge1;
    @ApiModelProperty(value = "年龄单位1")
    private String recAgeunit1;
    @ApiModelProperty(value = "年龄2")
    private Short recAge2;
    @ApiModelProperty(value = "年龄单位2")
    private String recAgeunit2;
    @ApiModelProperty(value = "性别")
    private String recGender;

    @TianAnDecryptField
    @ApiModelProperty(value = "住址")
    private String recAddress;

    @ApiModelProperty(value = "是否怀孕")
    private String gravidity;
    @ApiModelProperty(value = "就诊时间")
    private Date recTreTime;
    @ApiModelProperty(value = "就诊科室ID")
    private String deptId;
    @ApiModelProperty(value = "就诊科室名称")
    private String deptName;
    @ApiModelProperty(value = "医生UUID")
    private String docId;
    @ApiModelProperty(value = "医生姓名")
    private String docName;
    @ApiModelProperty(value = "APPID")
    private String appId;
    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;
    @ApiModelProperty(value = "医疗机构名称")
    private String insName;
    @ApiModelProperty(value = "医疗机构就诊记录ID，如：米市巷的就诊记录ID")
    private String insTreId;
    @ApiModelProperty(value = "病历类型（分两类：门诊、住院医嘱，对应值分别为1、2）")
    private String recTreType;
    @ApiModelProperty(value = "门诊号/住院号")
    private String clinicTypeNo;
    @ApiModelProperty("西医诊断ID")
    private String westernDiseaseId;
    @ApiModelProperty(value = "西医诊断结果")
    private String westernDisease;
    @ApiModelProperty(value = "疾病ID")
    private String disId;
    @ApiModelProperty(value = "疾病名称")
    private String disName;
    @ApiModelProperty(value = "证型ID")
    private String symId;
    @ApiModelProperty(value = "证型名称")
    private String symName;
    @ApiModelProperty(value = "治法字符串")
    private String theNames;
    @ApiModelProperty(value = "治法代码")
    private String theCode;
    @ApiModelProperty(value = "治法代码-前端需要")
    private String therapyCode;
    @ApiModelProperty(value = "主诉")
    private String patientContent;
    @ApiModelProperty(value = "现病史")
    private String nowDesc;
    @ApiModelProperty(value = "既往史")
    private String pastDesc;
    @ApiModelProperty(value = "家族史")
    private String familyDesc;
    @ApiModelProperty(value = "过敏史")
    private String allergyDesc;
    @ApiModelProperty(value = "舌像")
    private String tongue;
    @ApiModelProperty(value = "脉象")
    private String pulse;
    @ApiModelProperty(value = "中医四诊")
    private String fourDiagnosis;
    @ApiModelProperty(value = "体格检查")
    private String physical;
    @ApiModelProperty(value = "辅助检查")
    private String auxiliaryExam;
    @ApiModelProperty("治疗意见")
    private String treatmentAdvice;
    @ApiModelProperty(value = "初诊病历UUID")
    private String recFirstid;
    @ApiModelProperty(value = "诊次")
    private Integer visNum;
    @ApiModelProperty(value = "末次服药日期")
    private String recLastdate;
    @ApiModelProperty(value = "在线复诊（0不支持在线复诊  15：15天内在线复诊   90：3个月内在线复诊）")
    private Short recOnlineRediagnosis;
    @ApiModelProperty(value = "在线复诊有效时间（不支持在线复诊的填开方时间）")
    private String recRediagnosisDeadline;
    @ApiModelProperty(value = "审核类型（0为自动审核，1为人工审核）")
    private String checkType;
    @ApiModelProperty(value = "审核状态（-1 草稿中 0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "审核不通过原因")
    private String checkAdvise;


    // 挂号表信息
    @ApiModelProperty(value = "就诊医生姓名( 挂号表)")
    private String doctorName;
    @ApiModelProperty(value = "门诊类型（1门诊 2住院）( 挂号表)")
    private String clinicTypeId;
    @ApiModelProperty(value = "门诊类型名称( 挂号表)")
    private String clinicTypeName;
    @ApiModelProperty(value = "挂号费( 挂号表)")
    private String clinicTypeMoney;
    @ApiModelProperty(value = "挂号时间段（1上午  2下午）( 挂号表)")
    private String registerTimeArange;
    @ApiModelProperty(value = "就诊状态（0挂号未缴费，2待就诊，4已就诊，5已付款，6已退款，8已发药）( 挂号表)")
    private String registerDiagnosisStatus;
    @ApiModelProperty(value = "就诊号( 挂号表)")
    private String registerNum;
    @ApiModelProperty(value = "开方系统打开时间(修改处方时填，新开处方不填)( 挂号表)")
    private Date recOpenTime;
    @ApiModelProperty(value = "患者生日(患者表)")
    private String patientBirthday;

    @ApiModelProperty(value = "证件类型")
    private String patientCertificateType;

    @TianAnDecryptField
    @ApiModelProperty(value = "身份证号(患者表)")
    private String patientCertificate;

    @TianAnDecryptField
    @ApiModelProperty(value = "患者手机号(患者表)")
    private String patientMobile;

    @ApiModelProperty(value = "就诊卡号(患者表)")
    private String medicalCardNo;
    @ApiModelProperty(value = "第三方患者ID(患者表)")
    private String originPatientId;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人(配送表,遍历处方,哪个方子存在配送数据就显示到这里)")
    private String dcName;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人手机号(配送表)")
    private String dcMobile;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人地址(配送表)")
    private String dcAddress;

    @ApiModelProperty(value = "是否代煎 1是 0 否")
    private String decoctType;
    @ApiModelProperty(value = "是否膏方 1是 0 否")
    private String isProduction;
    @ApiModelProperty(value = "是否有中医电子病历 1是 0 否")
    private String isHasRecDetail;
    @ApiModelProperty(value = "his病历号 ")
    private String visitNo;

    @ApiModelProperty(value = "HIS疾病代码")
    private String disCodeHis;
    @ApiModelProperty(value = "HIS证型代码")
    private String symCodeHis;

    @ApiModelProperty(value = "保存his的医保类型中文")
    private String medicalTypeName;

    // 病历从表
    @ApiModelProperty(value = "病历从表集合")
    private List<TRecordDetail> recordDetailList;

    @ApiModelProperty(value = "处方表集合")
    private List<TPreRespVO> preList;   //处方表

    private String url;
    private String personalDesc;//个人史
}