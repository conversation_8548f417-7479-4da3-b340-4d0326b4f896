package com.jiuzhekan.cbkj.beans.referral;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TPreReferralSave implements Serializable{

//    @ApiModelProperty(value = "主键id")
//    private Long preReferralId;

    @ApiModelProperty(value = "医工体id")
    private String appId;


    @ApiModelProperty(value = "机构代码")
    private String insCode;

    @ApiModelProperty(value = "机构名称")
    private String insName;

    @ApiModelProperty(value = "医生id")
    private String doctorId;

    @ApiModelProperty(value = "医生姓名")
    private String doctorName;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患者性别（0未知1男2女9未说明）")
    private String patientGender;

    @ApiModelProperty(value = "患者性别（0未知1男2女9未说明）名称")
    private String  patientGenderName;

    @ApiModelProperty(value = "患者民族名称")
    private String patientNationName;

    @ApiModelProperty(value = "患者民族代码")
    private String patientNationCode;

    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "患者证件号")
    private String patientCertificate;

    @ApiModelProperty(value = "患者职业代码")
    private String patientOccupationCode;

    @ApiModelProperty(value = "患者职业名称")
    private String patientOccupationName;

    @ApiModelProperty(value = "疾病id")
    private String disId;

    @ApiModelProperty(value = "疾病名")
    private String disName;

    @ApiModelProperty(value = "证型id")
    private String symId;

    @ApiModelProperty(value = "证型名称")
    private String symName;

    @ApiModelProperty(value = "治法代码")
    private String theCode;

    @ApiModelProperty(value = "治法名称")
    private String theName;

    @ApiModelProperty(value = "转诊原因")
    private String referralResult;

    @ApiModelProperty(value = "主诉")
    private String patientContent;

    @ApiModelProperty(value = "现病史")
    private String nowDesc;

    @ApiModelProperty(value = "既往史")
    private String pastDesc;

    @ApiModelProperty(value = "体格检查")
    private String physical;

    @ApiModelProperty(value = "中医四诊")
    private String fourDiagnosis;

    @ApiModelProperty(value = "辅助检查")
    private String auxiliaryExam;

    @ApiModelProperty(value = "转诊图片地址多个图逗号分开")
    private String referralImages;

    @ApiModelProperty(value = "患者住址")
    private String patientAddress;

    private String deptId;

    private String deptName;
    private String westernDiseaseId;
    private String westernDisease;
    private String maritalCode;
    private String maritalName;

//    @ApiModelProperty(value = "转诊提交时间")
//    private Date referralTime;


}
