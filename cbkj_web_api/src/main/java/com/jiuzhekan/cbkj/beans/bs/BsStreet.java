package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel("街道")
public class BsStreet implements Serializable{

    private Integer streetId;

    @ApiModelProperty("区代码")
    private String areaCode;

    @ApiModelProperty("街道代码")
    private String streetCode;

    @ApiModelProperty("街道名称")
    private String streetName;

    @ApiModelProperty("简称")
    private String shortName;

    private String lng;

    private String lat;

    private Integer sort;

    private Date gmtCreate;

    private Date gmtModified;

    private String memo;

    private Integer dataState;


}
