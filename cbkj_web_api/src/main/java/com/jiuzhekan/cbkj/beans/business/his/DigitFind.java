package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DigitFind {


//    @ApiModelProperty("就诊卡号")
//    private String medicalCardNo;
//    private String patientName;
//
//    @ApiModelProperty("身份证号")
//    private String patientCertificate;
//    private String patientMobiLe;

    @ApiModelProperty("姓名|手机号|就诊卡号|就诊序号,其中一个。")
    private String searchKey;

    @ApiModelProperty("登记日期查询开始时间")
    private String startDate;
    @ApiModelProperty("登记日期查询结束时间")

    private String endDate;

//    @ApiModelProperty(value = "（新华）-1.待分配四诊仪器0.等待四诊仪数据 1.获取到四诊仪数据 2.已推送给HIS -2.推送失败")
//    private Integer infoStatus;

    @ApiModelProperty(value = "1已登记2已完成")
    private Integer dataStatus;

    @ApiModelProperty(hidden = true)
    private String registerId;

    @ApiModelProperty(value = "医共体ID")
    private String appId;
    @ApiModelProperty(value = "机构代码")
    private String insCode;
}
