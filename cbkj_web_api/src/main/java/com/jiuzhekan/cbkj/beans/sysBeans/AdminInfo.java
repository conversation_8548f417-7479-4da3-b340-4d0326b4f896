package com.jiuzhekan.cbkj.beans.sysBeans;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuzhekan.cbkj.common.exception.CustomRuntimeException;
import com.jiuzhekan.cbkj.common.exception.ExceptionUtils;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;


import java.io.Serializable;
import java.util.*;

/**
 * 管理员
 */
@ApiModel
@Data
@NoArgsConstructor
public class AdminInfo implements UserDetails, Serializable {
    private static final long serialVersionUID = -702132509116549375L;
    private String id;

    @ApiModelProperty(value = "账号")
    private String name;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "0禁用1启用2冻结")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(hidden = true)
    private String createId;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "最后登录IP")
    private String lastIp;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "手机号")
    private String phone;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "姓名")
    private String nameZh;

    @ApiModelProperty(value = "头像")
    private String userHeand;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "医联体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构ID")
    private String insCode;

    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "科室")
    private String deptName;

    @ApiModelProperty(value = "工号")
    private String employeeId;

    @Deprecated
    @ApiModelProperty(value = "第三方医生ID")
    private String originDoctorId;

    @ApiModelProperty(value = "有效时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;

    @ApiModelProperty(value = "创建人")
    private String createUsername;

    @ApiModelProperty(hidden = true)
    private String updateUser;

    @ApiModelProperty(value = "修改人")
    private String updateUsername;

    @ApiModelProperty(value = "删除时间")
    private Date delDate;

    @ApiModelProperty(hidden = true)
    private String delUser;

    @ApiModelProperty(value = "删除人")
    private String delUsername;

    @ApiModelProperty(hidden = true)
    private String isDel;

    @ApiModelProperty(value = "中医资质 1有0无")
    private Integer isQualifier;

    @ApiModelProperty(value = "特殊药品权限 1有0无")
    private String isSpecDrugQualifier;

    @ApiModelProperty(value = "HIS科室ID")
    private String depIdHis;

    @ApiModelProperty(value = "HIS科室名称")
    private String depNameHis;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "身份证号")
    private String userCertificate;

    @ApiModelProperty(value = "中医资质图片路径(多张图片用英文逗号分隔)")
    private String qualifierPicPath;

    @ApiModelProperty(value = "是否需要修改密码")
    private boolean needUpdatePwd;

    /*****************用户扩展属性****************/
    /**
     * 医生扩展信息  userExType 1输入码 2介绍 3协定方分享权限 4病历模板分享权限
     */
    @ApiModelProperty(hidden = true)
    private AdminInfoEx adminInfoEx;

    @ApiModelProperty(value = "输入码(1拼音 2五笔)")
    private String shuruma;


    /*****************用户扩展属性****************/

    private List<String> picList;

    @ApiModelProperty(hidden = true)
    private List<AdminRule> roles;

    private List<Object> adminRolePlatforms;

    @ApiModelProperty(hidden = true)
    private String rid;

    @ApiModelProperty(hidden = true)
    private String type;

    private SysAdminPractice practice;

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp == null ? null : lastIp.trim();
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public void setRoles(List<AdminRule> roles) {
        this.roles = roles;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();
        if (null != roles && roles.size() > 0) {
            for (AdminRule role : roles) {
                if (StringUtils.isNotBlank(role.getRname())) {
                    authorities.add(new SimpleGrantedAuthority(role.getRname()));
                }
            }
            return authorities;
        }
        return null;
    }

    @Override
    public String getUsername() {
        return this.name;
    }

    /**
     * 账户是否过期,过期无法验证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否被锁定或者解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否启用
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isEnabled() {
        if (this.status == null || this.status == 0 || this.status == 2) {
            return false;
        }
        return true;
    }

//    /**
//     * 初始化医联体医疗机构科室
//     *
//     * <AUTHOR>
//     * @date 2020/9/8
//     */
//    public void initAppInsDept() {
//        if (StringUtils.isBlank(this.getAppId())) {
//            this.setAppId(Constant.BASIC_APP_ID);
//            this.setInsCode(Constant.BASIC_INS_CODE);
//            this.setDepId(Constant.BASIC_DEPT_ID);
//            this.setDepName("");
//        } else if (StringUtils.isBlank(this.getInsCode())) {
//            this.setInsCode(Constant.BASIC_INS_CODE);
//            this.setDepId(Constant.BASIC_DEPT_ID);
//            this.setDepName("");
//        } else if (StringUtils.isBlank(this.getDepId())) {
//            this.setDepId(Constant.BASIC_DEPT_ID);
//            this.setDepName("");
//        }
//    }

    public void setQualifierPicPathFromPicList() {
        if (this.getPicList() != null && this.getPicList().size() > 0) {
            StringBuilder sb = new StringBuilder();
            for (String pic : this.getPicList()) {
                sb.append(pic).append(",");
            }
            this.setQualifierPicPath(sb.toString());
        }
    }

    public AdminInfo(Object ssoData) {
        if (ssoData instanceof HashMap) {
            Map ssoUser = (HashMap) ssoData;
            if (ssoUser.get("userId") == null) {
                ExceptionUtils.throwErrorCustomRuntimeException("综合平台用户信息异常");
            }
            this.id = ssoUser.get("userId").toString();
            this.name = ssoUser.get("userName") == null ? "" : ssoUser.get("userName").toString();
            this.nameZh = ssoUser.get("nameZh") == null ? "" : ssoUser.get("nameZh").toString();
            this.isQualifier = ssoUser.get("isQualifier") == null ? 0 : Integer.parseInt(ssoUser.get("isQualifier").toString());
            this.shuruma = ssoUser.get("isPyWb") == null ? "1" : ssoUser.get("isPyWb").toString();

            this.sex = ssoUser.get("sex") == null ? "" : ssoUser.get("sex").toString();

            this.appId = ssoUser.get("appId") == null ? "" : ssoUser.get("appId").toString();
            this.insCode = ssoUser.get("insCode") == null ? "" : ssoUser.get("insCode").toString();
            this.deptId = ssoUser.get("deptId") == null ? "" : ssoUser.get("deptId").toString();

            this.phone = ssoUser.get("phone") == null ? "" : ssoUser.get("phone").toString();
            this.employeeId = ssoUser.get("employeeId") == null ? "" : ssoUser.get("employeeId").toString();
            this.insName = ssoUser.get("insName") == null ? "" : ssoUser.get("insName").toString();
            this.needUpdatePwd = ssoUser.get("needUpdatePwd") != null && (boolean) ssoUser.get("needUpdatePwd");
            Object o = ssoUser.get("roles");
            if (o != null){
                ArrayList<Object> jsonObject = (ArrayList<Object>)ssoUser.get("roles");
                this.setAdminRolePlatforms(jsonObject);
            }
//            this.roles =  o == null ? null : JSONObject.parseArray(JSONObject.toJSONString(ssoUser.get("roles")), AdminRule.class);
            this.adminInfoEx = JSONObject.parseObject(JSONObject.toJSONString(ssoUser.get("adminInfoEx")), AdminInfoEx.class);
            if (this.adminInfoEx == null) {
                this.adminInfoEx = new AdminInfoEx();
            }
        }
    }
}