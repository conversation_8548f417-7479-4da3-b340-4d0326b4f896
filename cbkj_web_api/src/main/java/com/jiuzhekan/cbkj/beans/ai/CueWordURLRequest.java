package com.jiuzhekan.cbkj.beans.ai;

import com.jiuzhekan.cbkj.controller.request.MedicalHistorySaveRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/5 11:38
 * @Version 1.0
 */
@Data
@ApiModel
public class CueWordURLRequest {
//    private String context;

    @ApiModelProperty(value = "ai:AI方解,diagnostic_base:诊断依据 record_analysis:AI病历解析 symptom_analysis:就诊症状分析" )
    private String cueWordType;

    @ApiModelProperty(value = "ai病历质控传这个对象")
    private MedicalHistorySaveRequest medicalHistorySaveRequest;




    @ApiModelProperty(value = "疾病名称")
    private String disName;
    @ApiModelProperty(value = "疾病id")
    private String disId;
    @ApiModelProperty(value = "证型名称")
    private String symName;

    @ApiModelProperty(value = "证型id")
    private String symId;

    @ApiModelProperty(value = "治法名称")
    private String theName;

    @ApiModelProperty(value = "治法代码")
    private String therapyCode;

    @ApiModelProperty(value = "病历id")
    private String recId;

    private List<CueWordPres> cueWordPresList;


}
