package com.jiuzhekan.cbkj.beans.statistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class StatisticsInstitutionWorkload implements Serializable{

    @ApiModelProperty(value = "自增主键")
    private Integer id;

    @ApiModelProperty(value = "医联体ID")
    private String appId;

    @ApiModelProperty(value = "医联体")
    private String appName;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构")
    private String insName;

    @ApiModelProperty(value = "就诊人次（月）")
    private String regCount;

    @ApiModelProperty(value = "内服中药方（月）")
    private String nfCount;

    @ApiModelProperty(value = "外用中药方（月）")
    private String wyCount;

    @ApiModelProperty(value = "中成药方（月）")
    private String zcCount;

    @ApiModelProperty(value = "适宜技术方（月）")
    private String syCount;

    @ApiModelProperty(value = "总开方数（月）")
    @JsonProperty("pCount")
    private String pcount;

    @ApiModelProperty(value = "就诊人次（年）")
    private String qregCount;

    @ApiModelProperty(value = "内服中药方（年）")
    private String qnfCount;

    @ApiModelProperty(value = "外用中药方（年）")
    private String qwyCount;

    @ApiModelProperty(value = "中成药方（年）")
    private String qzcCount;

    @ApiModelProperty(value = "适宜技术方（年）")
    private String qsyCount;

    @ApiModelProperty(value = "总开方数（年）")
    private String qpCount;

    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入时间")
    private Date insertDate;


}
