package com.jiuzhekan.cbkj.beans.business.zkxcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcMz implements Serializable{

    @ApiModelProperty(value = "")
    private Integer mzId;

    @ApiModelProperty(value = "因素枚举")
    private String factorEnumeration;

    @ApiModelProperty(value = "base64 图片")
    private String pulseQuantificationImg;
    /**
     * 偏差图
     */
    @ApiModelProperty(value = "base64 偏差图")
    private String pulseDeviationImg;

    @ApiModelProperty(value = "症状提示")
    private String symptomTips;

    @ApiModelProperty(value = "")
    private Integer collectId;
    private String registerId;

    /**
     * 整体脉象结论
     */
    private String totalPulseFeatureDesc;


}
