package com.jiuzhekan.cbkj.beans.zkxc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 14:15
 * @Version 1.0
 */
@Data
@ApiModel
public class MZHistoryDAOTWO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;


    @ApiModelProperty(value = "脉诊（左右手的寸关尺）主键id")
    private Integer id;
    private Integer collectId;

    @ApiModelProperty(value = "中科传过来的不知道干啥用")
    private boolean collectedData;


    @ApiModelProperty(value = "患者节律值")
    private Float jieLv;

    @ApiModelProperty(value = "节律标准值")
    private String jieLvStandard;

    @ApiModelProperty(value = "患者紧张度数值")
    private Float jinZhangDu;

    @ApiModelProperty(value = "紧张度标准值")
    private String jinZhangDuStandard;

    @ApiModelProperty(value = "患者流利度数值")
    private Float liuLiDu;

    @ApiModelProperty(value = "流利度标准值")
    private String liuLiDuStandard;

    @ApiModelProperty(value = "患者脉力")
    private Float maiLi;

    @ApiModelProperty(value = "脉力偏差值")
    private Float maiLiDeviation;

    @ApiModelProperty(value = "")
    private Float maiLiDownLineValue;

    @ApiModelProperty(value = "脉力标准值")
    private String maiLiStandard;

    @ApiModelProperty(value = "")
    private Float maiLiUpLineValue;

    @ApiModelProperty(value = "脉率值")
    private Float maiLv;

    @ApiModelProperty(value = "脉率偏差值")
    private Float maiLvDeviation;

    @ApiModelProperty(value = "")
    private Float maiLvDownLineValue;

    @ApiModelProperty(value = "脉率标准值")
    private String maiLvStandard;

    @ApiModelProperty(value = "")
    private Float maiLvUpLineValue;

    @ApiModelProperty(value = "脉位值")
    private Float maiWei;
    private int maiWeiDownUp;
    private int maiLvDownUp;
    private int jieLvDownUp;
    private int maiLiDownUp;
    private int jinZhangDuDownUp;
    private int liuLiDuDownUp;

    private void setMaiWei(){
            if (maiWei  > maiWeiMin && maiWei < maiWeiMax ){
             this.maiWeiDownUp = 0;
            }
            else if ( maiWei> maiWeiMax ){
                this.maiWeiDownUp = 1;
            }
            else if ( maiWei < maiWeiMin ){
                this.maiWeiDownUp = -1;
            }
    }
    private void setMaiLv(){
        if (maiLv  > maiLvMin && maiLv < maiLvMax ){
            this.maiLvDownUp = 0;
        }
        else if ( maiLv> maiLvMax ){
            this.maiLvDownUp = 1;
        }
        else if ( maiLv < maiLvMin ){
            this.maiLvDownUp = -1;
        }
    }

    private void setJieLv(){
        if (jieLv  > jieLvMin && jieLv < jieLvMax ){
            this.jieLvDownUp = 0;
        }
        else if ( jieLv> jieLvMax ){
            this.jieLvDownUp = 1;
        }
        else if ( jieLv < jieLvMin ){
            this.jieLvDownUp = -1;
        }
    }

    private void setMaiLi(){
        if (maiLi  > maiLiMin && maiLi < maiLiMax ){
            this.maiLiDownUp = 0;
        }
        else if ( maiLi> jieLvMax ){
            this.maiLiDownUp = 1;
        }
        else if ( maiLi < jieLvMin ){
            this.maiLiDownUp = -1;
        }
    }

    private void setJinZhangDu(){
        if (jinZhangDu  > jinZhangDuMax  ){
            this.jinZhangDuDownUp = 1;
        }

        else if ( jinZhangDu < jinZhangDuMax ){
            this.jinZhangDuDownUp = -1;
        }
        else {
            this.jinZhangDuDownUp = 0;
        }
    }

    private void setLiuLiDu(){
        if (liuLiDu  > liuLiDuMax  ){
            this.liuLiDuDownUp = 1;
        }

        else if ( liuLiDu < liuLiDuMax ){
            this.liuLiDuDownUp = -1;
        }
        else {
            this.liuLiDuDownUp = 0;
        }
    }

    public void initDownUp(){
        setMaiWei();
        setMaiLv();
        setJieLv();
        setMaiLi();
        setJinZhangDu();
        setLiuLiDu();
    }


    @ApiModelProperty(value = "脉位偏差值")
    private Float maiWeiDeviation;

    @ApiModelProperty(value = "")
    private Float maiWeiDownLineValue;

    @ApiModelProperty(value = "脉位标准值")
    private String maiWeiStandard;

    @ApiModelProperty(value = "")
    private Float maiWeiUpLineValue;

//    @ApiModelProperty(value = "")
//    private String optPulse1;

    @ApiModelProperty(value = "脉象识别结果")
    private String pulsePotential;

    @ApiModelProperty(value = "")
    private Float veinElementDownLineValue;

    @ApiModelProperty(value = "")
    private Float veinElementUpLineValue;

    @ApiModelProperty(value = "脉象要素分析图（弦、滑、正常）")
    private String veinElements;

    @ApiModelProperty(value = "脉诊的外层对象主键id")
    private Integer mzId;

    @ApiModelProperty(value = "1.左尺2.左寸3.左关4.右尺5.右寸6.右关")
    private Integer mzResultType;
    @ApiModelProperty(value = "脉位特征")
    private String maiweiFeatureDesc;

    @ApiModelProperty(value = "脉率特征")
    private String mailvFeatureDesc;

    @ApiModelProperty(value = "节律特征")
    private String jielvFeatureDesc;

    @ApiModelProperty(value = "脉力特征")
    private String mailiFeatureDesc;

    @ApiModelProperty(value = "紧张度特征")
    private String jinzhangduFeatureDesc;
    @ApiModelProperty(value = "流利度特征")
    private String liuliduFeatureDesc;

    @ApiModelProperty(value = "脉象标定")
    private String pulsePotentialCalibration;
//    @ApiModelProperty(value = "脉象标定Code")
//    private String pulsePotentialCalibrationCode;


    private Float maiWeiMin = 200f;
    private Float maiWeiMax = 350f;

    private Float maiLvMin = 60f;
    private Float maiLvMax = 100f;

    private Float jieLvMin = 0f;
    private Float jieLvMax = 0.15f;

    private Float maiLiMin = 16f;
    private Float maiLiMax = 25f;

    private Float jinZhangDuMax = 0.7f;
    private Float liuLiDuMax = 0.6f;




}
