package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.beans.business.prescription.TPreDecoction;
import com.jiuzhekan.cbkj.beans.business.prescription.TPreExpress;
import com.jiuzhekan.cbkj.beans.business.prescription.TPreProduction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: pre_api
 * @description: 就诊记录用小处方表
 * @author: wangtao
 * @create: 2021-03-29 17:40
 **/
@Data
public class PrescriptionMin implements Serializable {
    private String preId;
    private String preType;
    private String preNum;
    private String preDescriptionId;//服法id
    private String preDescription;//服法
    private String preFrequencyId;//频次id
    private String preFrequency;//频次
    private String preSmokeTypeId;//外用用法id
    private String preSmokeType;//外用用法
    private String preUseTimeId;//服药时间ID
    private String preUseTimeDes;//服药时间说明
    private String preDoctorName;
    // @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String preTime;
    private String preNbag;
    private String preNMl;
    private String preNMlName;
    private String preSmokeInstrument;
    private String preSmokeInstrumentId;
    private String acuTypeId;
    private String acuType;
    private String acuProjectId;
    private String acuProject;
    private String preMatType;
    private String storeId;
    private String preDecoctNum;
    private String acuOperation;
    private String preAdvice;
    private String isProduction;
    private String isSpeaialDis;
    private String decoctType;
    private String dcType;
    private String dcId;
    private  int sumNeedle;
    private String preOrigin;
    private String preOriginId;
    private String preOriginName;
    private Boolean preOriginUpdate = true;
    private String productionType;
    private String productionTypeId;
    private String beganTime;
    private String endTime;
    private List<PrescriptionItemMin> prescriptionItemMinList;
    private List<TPrescriptionAcuItem> prescriptionAcuItemList;
    private List<TPrescriptionPreparationItem> preparationItemList;

    /*****代煎*****/
    @ApiModelProperty(value = "处方代煎信息")
    private TPreDecoction decoction;
    /*****制膏*****/
    @ApiModelProperty(value = "处方制膏信息")
    private TPreProduction production;
    /*****配送*****/
    @ApiModelProperty(value = "处方配送信息")
    private TPreExpress express;
}
