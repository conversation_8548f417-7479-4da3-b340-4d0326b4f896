package com.jiuzhekan.cbkj.beans.http;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 2.3提交专病数据计算并返回处方信息 ,接收此接口请求参数
 */
@Data
@ApiModel(value = "专病接口请求参数",description = " 2.3提交专病数据计算并返回处方信息 ,接收此接口请求参数")
public class ZbReqVO  implements Serializable {
    @ApiModelProperty(value = "患者姓名")
    private String name;
    @ApiModelProperty(value = "患者年龄，必须整数，单位年")
    private Integer age;
    @ApiModelProperty(value = "性别，男或女")
    private String sex;
    @ApiModelProperty(value = "体质（好，一般，差）")
    private String radioConstitution;
    @ApiModelProperty(value = "专病代码")
    private String diseaseCode;
    @ApiModelProperty(value = "医生姓名")
    private String doctorName;
    @ApiModelProperty(value = "由国医大师系统分配给第三方系统接入的唯一标识")
    private String apiToken;
    @ApiModelProperty(value = "提交内容，键值对的格式提交")
    private String jsonStr;
}
