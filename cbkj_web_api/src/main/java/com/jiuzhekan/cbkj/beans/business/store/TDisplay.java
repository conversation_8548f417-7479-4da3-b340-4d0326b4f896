package com.jiuzhekan.cbkj.beans.business.store;

import com.jiuzhekan.cbkj.beans.business.his.THisRecord;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCode;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Data
@ApiModel
@NoArgsConstructor
public class TDisplay implements Serializable {

    @ApiModelProperty(value = "显示ID")
    private String displayId;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "药品剂型（1散装饮片 2散装颗粒 4小包装饮片 5小包装颗粒 6膏方），多个用英文逗号分隔")
    private String preMatType;

    @ApiModelProperty(value = "药品默认用法（如饮片煎服，颗粒冲服）")
    private String defaultUsage;

    @ApiModelProperty(value = "药房ID")
    private String storeId;

    @ApiModelProperty(value = "药房名称")
    private String storeName;

    @ApiModelProperty(value = "药品目录id")
    private String drugId;


    @ApiModelProperty(" 代煎信息")
    private TDisplayDecoction decoction;

    @ApiModelProperty(" 药房费用配置信息")
    private TDisplayProduction production;

    @ApiModelProperty(" 药房制膏信息")
    private TDisplayExpress express;

    @ApiModelProperty(" 院内治疗信息")
    private TDisplayHospitalTreatment hospitalTreatment;

    @ApiModelProperty(value = "内服处方服法")
    private TSysCode PRESCRIPTION_INTERNAL_USAGE;
    @ApiModelProperty(value = "内服处方频次")
    private TSysCode PRESCRIPTION_INTERNAL_RATE;
    @ApiModelProperty(value = "内服处方服药时间")
    private TSysCode PRESCRIPTION_INTERNAL_TIME;
    @ApiModelProperty(value = "内服处方浓煎-制剂每剂加工量也是这个")
    private TSysCode PRESCRIPTION_INTERNAL_ML;
    @ApiModelProperty(value = "外用处方浓煎-制剂患者每次用量也是这个")
    private TSysCode PRESCRIPTION_EXTERNAL_ML;
    @ApiModelProperty(value = "外用方式")
    private TSysCode PRESCRIPTION_EXTERNAL_MODE;
    @ApiModelProperty(value = "熏蒸仪选择")
    private TSysCode PRESCRIPTION_EXTERNAL_OBJECT;
    @ApiModelProperty(value = "外用处方频次")
    private TSysCode PRESCRIPTION_EXTERNAL_RATE;
    @ApiModelProperty(value = "适宜技术方类型")
    private TSysCode PRESCRIPTION_ACUPOINT_MODE;
    @ApiModelProperty(value = "针刺项目")
    private TSysCode PRESCRIPTION_ACUPOINT_OBJECT;
    @ApiModelProperty(value = "膏方有无糖")
    private TSysCode PRESCRIPTION_INTERNAL_PRODUCTION_TYPE;
    @ApiModelProperty(value = "自提点类型")
    private TSysCode DESIGNATED_DELIVERY;

//    @ApiModelProperty(value = " 门诊剂型")
//    private TSysCode OUTPATIENT_DOSAGE_FORM;
    @ApiModelProperty(value = " 制剂-剂型")
    private TSysCode PRESCRIPTION_DOSAGE_FORM;

    @ApiModelProperty(value = "是否预扣计算0否1是")
    private String withholdSwitch;

    @ApiModelProperty(value = "内服服药时间开关 0关 1开，默认开")
    private Integer oralMedicationTimeSwitch;

    @ApiModelProperty(value = "加急标志 0 关 1开")
    private Integer urgentSign;

    @ApiModelProperty(value = "药品开方多规格下拉开关 1是 0否")
    private Integer manySpeSwitch;
    @ApiModelProperty(value = "处方保存接口校验库存开关1是0否")
    private String preStockSwitch;


    @ApiModelProperty(value = "剂型-开关状态0开启1关闭")
    private Integer dosageStatus;

    @ApiModelProperty(value = "剂型-0录入入口开启，1录入入口关闭")
    private Integer expenseEntry;


    public TDisplay(TDisplayMappingResponse response) {
        this.displayId = response.getDisplayId();
        this.displayName = response.getDisplayName();
        this.preMatType = response.getMatType();
        this.storeId = response.getPhaId();
        this.storeName = response.getPhaName();
        this.drugId = response.getDrugId();
    }

    public void transDisplayDecoctionProductionExpress(TDisplayMappingResponse response, String outpatientOrHospitalization) {

        if (StringUtils.isBlank(outpatientOrHospitalization)) {
            return;
        }

        if (response.getDecoctionList() != null) {
            TDisplayDecoction decoction = response.getDecoctionList().stream()
                    .filter(d -> outpatientOrHospitalization.equals(d.getOutpatientOrHospitalization()))
                    .peek(d -> {

                        Map<String, List<TDisplayMoneySetting>> settingsMap = d.getSettingList().stream()
                                .filter(s -> outpatientOrHospitalization.equals(s.getOutpatientOrHospitalization()))
                                .collect(Collectors.groupingBy(TDisplayMoneySetting::getCurrencyOrFormula));

                        d.setUsuallyMoneySetting(settingsMap.get(Constant.BASIC_STRING_ONE));
                        d.setFormulaMoneySetting(settingsMap.get(Constant.BASIC_STRING_TWO));

                    }).findFirst().orElse(null);
            this.setDecoction(decoction);
        }

        if (response.getProductionList() != null) {
            TDisplayProduction production = response.getProductionList().stream()
                    .filter(d -> outpatientOrHospitalization.equals(d.getOutpatientOrHospitalization()))
                    .peek(d -> {

                        //医保患者是否显示膏方
                        if (Constant.BASIC_STRING_ZERO.equals(d.getShowProduction())) {
                            TRegister register = AdminUtils.getCurrentRegister();
                            if (register != null) {
                                THisRecord hisRecord = register.getHisRecord();
                                if (hisRecord != null) {
                                    //医保患者
                                    if (Constant.BASIC_STRING_ZERO.equals(hisRecord.getIsOwnExp())) {

                                        d.setShowProduction(d.getIsShowProduction());
                                    }
                                }
                            }
                        }

                        Map<String, List<TDisplayMoneySetting>> settingsMap = d.getSettingList().stream()
                                .filter(s -> outpatientOrHospitalization.equals(s.getOutpatientOrHospitalization()))
                                .collect(Collectors.groupingBy(TDisplayMoneySetting::getCurrencyOrFormula));

                        d.setUsuallyMoneySetting(settingsMap.get(Constant.BASIC_STRING_ONE));
                        d.setFormulaMoneySetting(settingsMap.get(Constant.BASIC_STRING_TWO));

                    }).findFirst().orElse(null);
            this.setProduction(production);
        }

        if (response.getExpressList() != null) {
            TDisplayExpress express = response.getExpressList().stream()
                    .filter(d -> outpatientOrHospitalization.equals(d.getOutpatientOrHospitalization()))
                    .peek(d -> {
                        Map<String, List<TDisplayMoneySetting>> settingsMap = d.getSettingList().stream()
                                .filter(s -> outpatientOrHospitalization.equals(s.getOutpatientOrHospitalization()))
                                .collect(Collectors.groupingBy(TDisplayMoneySetting::getCurrencyOrFormula));

                        d.setUsuallyMoneySetting(settingsMap.get(Constant.BASIC_STRING_ONE));

                    }).findFirst().orElse(null);

            if (express != null){
                if (StringUtils.isBlank(express.getDcTypeSubclass())){
                    express.setDcTypeSubclass(Constant.BASIC_STRING_ONE);
                }
            }

            this.setExpress(express);
        }
    }

}