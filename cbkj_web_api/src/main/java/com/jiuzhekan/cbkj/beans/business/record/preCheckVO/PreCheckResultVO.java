package com.jiuzhekan.cbkj.beans.business.record.preCheckVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "处方审核结果")
public class PreCheckResultVO implements Serializable {
    @ApiModelProperty(value = "病历id")
    private String recId;
    @ApiModelProperty(value = "挂号id")
    private String registerId;
    @ApiModelProperty(value = "姓名")
    private String recName;
    @ApiModelProperty(value = "病历审核状态,每条数据都一样 （0未审核 1审核通过 2审核未通过）")
    private String recIsCheck;
    @ApiModelProperty(value = "病历审核不通过原因")
    private String recCheckAdvise;
    @ApiModelProperty(value = "处方id")
    private String preId;
    @ApiModelProperty(value = "处方编号")
    private String preNo;
    @ApiModelProperty(value = "处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方）")
    private String preType;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "审核不通过原因")
    private String checkAdvise;
    @ApiModelProperty(value = "单剂金额")
    private String preSingleMoney;
    @ApiModelProperty(value = "药品总金额")
    private String matTolMoney;
    @ApiModelProperty(value = "处方总金额")
    private String preTolMoney;
    @ApiModelProperty(value = "处方实收总金额")
    private String preTolMoneyReal;

    @ApiModelProperty(value = "代煎费")
    private String preDecoctionFee;
    @ApiModelProperty(value = "制膏费")
    private String preProductionFee;
    @ApiModelProperty(value = "配送费")
    private String preExpressFee;
    @ApiModelProperty(value = "治疗费")
    private String preSmoMoney;

    private String recExtType;
}