package com.jiuzhekan.cbkj.beans.business.record;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel(value = "版本维护类",description = "版本维护类")

public class TBusinessEdition implements Serializable {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "*** 版本号 ***")
    private String editionNum;

    @ApiModelProperty(value = "发布内容")
    private String editionContent;

    @ApiModelProperty(value = "发布时间")
    private Date editionTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者ID")
    private String createUserId;

    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改者ID")
    private String updateUserId;

    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

    @ApiModelProperty(value = "删除者ID")
    private String deleteUserId;


    @ApiModelProperty(value = "是否删除（0否 1是）")
    private Byte isDel;

    @ApiModelProperty(value = "版本类型（1.系统更新2.知识库更新）")
    private String editionType;
}