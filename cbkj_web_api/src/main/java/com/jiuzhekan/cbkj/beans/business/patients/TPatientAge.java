package com.jiuzhekan.cbkj.beans.business.patients;


import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * @Auther: guowei
 * @Date: 2020/8/11 14:39
 * @Description:
 */
@Data
public class TPatientAge implements Serializable {
    Short age1;
    String ageUnit1;
    Short age2;
    String ageUnit2;

    public String getAge() {
        String age = "";
        if (age1 != null && StringUtils.isNotBlank(ageUnit1)) {
            age += age1 + ageUnit1;
        }
        if (age2 != null && StringUtils.isNotBlank(ageUnit2)) {
            age += age2 + ageUnit2;
        }
        return age;
    }

    public Short getYear() {
        if ("岁".equals(ageUnit1) && age1 != null) {
            return age1;
        }
        return 0;
    }
}
