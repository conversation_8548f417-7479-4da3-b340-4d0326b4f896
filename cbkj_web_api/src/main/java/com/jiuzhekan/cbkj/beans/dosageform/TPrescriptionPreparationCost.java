package com.jiuzhekan.cbkj.beans.dosageform;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TPrescriptionPreparationCost implements Serializable {

    @ApiModelProperty(value = "处方制剂收费明细id")
    private String preparationCostId;

    @ApiModelProperty(value = "计价方式（按顺序从1开始）：" +
            "按剂收取：按照处方剂数，计算公式：数量=剂数；总价=剂数*单价；" +
            "按斤收取：按照处方总重量收取，计算公式：数量=(处方总重量g/500)；总价=(处方总重量g/500)*单价；" +
            "按药味数收取：按照药味数收取，计算公式：数量=药味数；总价=药味数*单价；按克收费：按照处方总重量收取，计算公式：数量=处方总重量；总价=(处方总重量g)*单价；" +
            "按次收取：处方张数计价，计算公式:数量=1；总价=1*单价"+
            "按百克收取：按照处方总重量收取，计算公式：数量=(处方总重量g/100)；总价=(处方总重量g/100)*单价；")
    private Integer pricingMethod;

    @ApiModelProperty(value = "收费项目代码")
    private String chargeItemCode;

    @ApiModelProperty(value = "收费项目主键id")
    private String chargeItemId;

    @ApiModelProperty(value = "收费项目名称")
    private String chargeItemName;

    @ApiModelProperty(value = "收费项目明细主键id")
    private String chargeItemDetailId;

    @ApiModelProperty(value = "收费明细编码")
    private String itemDetailCode;

    @ApiModelProperty(value = "收费明细名称")
    private String itemDetailName;

    @ApiModelProperty(value = "单价")
    private BigDecimal itemDetailSinglePrice;

    @ApiModelProperty(value = "单位")
    private String itemDetailSingleUnit;

    @ApiModelProperty(value = "前置审核1关闭0开启")
    private Integer itemDetailPreCheck;

    @ApiModelProperty(value = "执行科室-启用执行：1关闭 0执行")
    private Integer itemDetailExecuteStatus;

    @ApiModelProperty(value = "项目内涵")
    private String itemDetailInnerText;

    @ApiModelProperty(value = "备注")
    private String itemDetailNotes;

    @ApiModelProperty(value = "项目医保编码")
    private String medicalInsuranceCode;

    @ApiModelProperty(value = "项目医保国家（区域）编码")
    private String medicalInsuranceCountryRegionCode;

    @ApiModelProperty(value = "处方制剂收费主表id")
    private String preparationId;

    @ApiModelProperty(value = "")
    private String preId;

    @ApiModelProperty(value = "")
    private String preNo;

    @ApiModelProperty(value = "插入时间")
    private Date createTime;

    @ApiModelProperty(value = "数量")
    private BigDecimal preparationCostNum;

    @ApiModelProperty(value = "总金额")
    private BigDecimal preparationCostTotalPrice;

    @ApiModelProperty(value = "制剂收费明细执行科室信息")
    private List<TPrescriptionPreparationCostDept> costDeptList;
}
