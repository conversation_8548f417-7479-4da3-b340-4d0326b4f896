package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.beans.business.setting.TBusinessAnnex;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "建议收集类" , description = "建议收集类,自行选择要传递或者接收的参数")
public class TBusinessProposal implements Serializable{

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者ID")
    private String createUserId;

    @ApiModelProperty(value = "创建者姓名 (即医生姓名)")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改者ID")
    private String updateUserId;

    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

    @ApiModelProperty(value = "删除者ID")
    private String deleteUserId;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "医联体名称")
    private String appName;

    @ApiModelProperty(value = "医疗机构编码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    @ApiModelProperty(value = "编号")
    private Integer proposalNum;

    @ApiModelProperty(value = "标题")
    private String proposalTitle;

    @ApiModelProperty(value = "建议内容")
    private String proposalContent;

    @ApiModelProperty(value = "联系方式")
    private String proposalLiaison;

    @ApiModelProperty(value = "公开状态：0公开，1匿名")
    private Byte proposalOpenState;

    @ApiModelProperty(value = "受理状态：0受理，1未受理 (如果多选框选中两个就不要传值)")
    private Byte proposalReceiveState;

    @ApiModelProperty(value = "受理意见")
    private String proposalReceiveOpinion;

    @ApiModelProperty(value = "受理人ID")
    private String proposalReceiveId;

    @ApiModelProperty(value = "受理人姓名")
    private String proposalReceiveName;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private Byte isDel;

    /**
     * @Description :
     * <AUTHOR> xhq
     * @updateTime : 2020/2/26 14:39
     */
    @ApiModelProperty(value = "医生(创建者)工号")
    private String  employeeId;

    @TianAnDecryptField
    @ApiModelProperty(value = "用户预留手机号")
    private String phone;

    @ApiModelProperty(value = "附件集合")
    private List<TBusinessAnnex> annexList;
}