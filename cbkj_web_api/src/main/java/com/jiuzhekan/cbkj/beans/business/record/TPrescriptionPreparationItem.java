package com.jiuzhekan.cbkj.beans.business.record;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TPrescriptionPreparationItem implements Serializable{

    @ApiModelProperty(value = "处方明细UUID")
    private String preItemId;

    @ApiModelProperty(value = "处方UUID")
    private String preId;

    @ApiModelProperty(value = "统一药品代码（由开方系统生成）")
    private String yaopindmTy;

    @ApiModelProperty(value = "药品目录代码-HIS")
    private String ypmlHis;

    @ApiModelProperty(value = "药品目录代码-中心")
    private String ypmlCenter;

    @ApiModelProperty(value = "药品代码-HIS")
    private String ypdmHis;

    @ApiModelProperty(value = "药品代码-中心")
    private String ypdmCenter;

    @ApiModelProperty(value = "药品名称-HIS")
    private String ypmcHis;

    @ApiModelProperty(value = "药品名称-中心")
    private String ypmcCenter;

    @ApiModelProperty(value = "药品规格-HIS")
    private String ypggHis;

    @ApiModelProperty(value = "药品规格-中心")
    private String ypggCenter;

    @ApiModelProperty(value = "产地ID（存中心药房的）")
    private String cdidCenter;

    @ApiModelProperty(value = "产地名称（存中心药房的）")
    private String cdmcCenter;

    @ApiModelProperty(value = "每次用量")
    private BigDecimal matDose;

    @ApiModelProperty(value = "一次剂量单位ID")
    private String matDoseunitId;

    @ApiModelProperty(value = "一次剂量单位")
    private String matDoseunit;

    @ApiModelProperty(value = "天数")
    private Short matDay;

    @ApiModelProperty(value = "数量")
    private BigDecimal matNum;

    @ApiModelProperty(value = "包装单位（存中心药房的）")
    private String bzdwHis;

    @ApiModelProperty(value = "用法ID")
    private String usageId;

    @ApiModelProperty(value = "用法名称")
    private String usage;

    @ApiModelProperty(value = "频次ID")
    private String matFrequencyId;

    @ApiModelProperty(value = "频次")
    private String matFrequency;

    @ApiModelProperty(value = "频次系数")
    private String matFrequencyRate;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "序号")
    private Integer matSeqn;

    @ApiModelProperty(value = "是否医保（0自付 1医保）")
    private String isInsurance;

    @ApiModelProperty(value = "")
    private Date insertTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "药房ID")
    private String centerStoreId;

    private String patientId;

    @ApiModelProperty(value = "日最大开药量")
    private BigDecimal dailyMaxNum;

    @ApiModelProperty(value = "日已开数量")
    private BigDecimal dailyUsedNum;
    @ApiModelProperty("中心药房药品库存数量")
    private BigDecimal centerKucunsl;
    @ApiModelProperty("中心药房药品库存数量")
    private Double zhuanhuanxs;

}
