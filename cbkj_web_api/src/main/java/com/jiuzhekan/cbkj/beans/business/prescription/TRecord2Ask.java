package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TRecord2Ask implements Serializable{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "病历编号")
    private String recId;

    @ApiModelProperty(value = "标签编号")
    private String tagNo;

    @ApiModelProperty(value = "饮食与口味代码")
    private String wzysykwdm;

    @ApiModelProperty(value = "饮食与口味其他")
    private String wzysykwqt;

    @ApiModelProperty(value = "饮食与口味")
    private String wzysykw;

    @ApiModelProperty(value = "睡眠代码")
    private String wzsmdm;

    @ApiModelProperty(value = "睡眠其他")
    private String wzsmqt;

    @ApiModelProperty(value = "睡眠")
    private String wzsm;

    @ApiModelProperty(value = "大便代码")
    private String wzdbdm;

    @ApiModelProperty(value = "大便其他")
    private String wzdbqt;

    @ApiModelProperty(value = "大便")
    private String wzdb;

    @ApiModelProperty(value = "寒热")
    private String wzhr;

    @ApiModelProperty(value = "寒热其他")
    private String wzhrqt;

    @ApiModelProperty(value = "出汗")
    private String wzch;

    @ApiModelProperty(value = "出汗其他")
    private String wzchqt;

    @ApiModelProperty(value = "头身")
    private String wzts;

    @ApiModelProperty(value = "头身其他")
    private String wztsqt;

    @ApiModelProperty(value = "胸胁脘腹")
    private String wzxxwf;

    @ApiModelProperty(value = "胸胁脘腹其他")
    private String wzxxwfqt;

    @ApiModelProperty(value = "耳目")
    private String wzem;

    @ApiModelProperty(value = "耳目其他")
    private String wzemqt;

    @ApiModelProperty(value = "小便")
    private String wzxb;

    @ApiModelProperty(value = "小便其他")
    private String wzxbqt;

    @ApiModelProperty(value = "妇女")
    private String wzfn;

    @ApiModelProperty(value = "妇女其他")
    private String wzfnqt;

    @ApiModelProperty(value = "小儿")
    private String wzxe;

    @ApiModelProperty(value = "小儿其他")
    private String wzxeqt;

    @ApiModelProperty(value = "其他")
    private String wzqt3;

    @ApiModelProperty(value = "状态【0:可用，1:禁用】")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;
    @ApiModelProperty(value = "寒热代码")
    private String wzhrdm;
    @ApiModelProperty(value = "出汗代码")
    private String wzchdm;
    @ApiModelProperty(value = "头身代码")
    private String wztsdm;
    @ApiModelProperty(value = "胸胁脘腹代码")
    private String wzxxwfdm;
    @ApiModelProperty(value = "耳目代码")
    private String wzemdm;
    @ApiModelProperty(value = "小便代码")
    private String wzxbdm;
    @ApiModelProperty(value = "妇女代码")
    private String wzfndm;
    @ApiModelProperty(value = "小儿代码")
    private String wzxedm;

    private Integer equipmentPatientId;

}
