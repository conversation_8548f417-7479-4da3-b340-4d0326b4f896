package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class DigitCheck {
//    @ApiModelProperty("患者姓名")
//    private String patientName;
//    @ApiModelProperty("患者电话")
//    private String patientMobile;
//    @ApiModelProperty("就诊卡号")
//    private String medicalCardNo;
//    @ApiModelProperty("就诊序号")
//    private String visitNo;


    @ApiModelProperty("姓名|手机号|就诊卡号|就诊序号,其中一个。")
    private String searchKey;

}
