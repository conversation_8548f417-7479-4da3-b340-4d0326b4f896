package com.jiuzhekan.cbkj.beans.business.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "用户体质辨识分组结果类",description = "用户体质辨识分组结果类")
public class TUserAnalysisGroupResult implements Serializable {
    @ApiModelProperty(value = "辨识分组结果ID")
    private String analyTypeId;
    @ApiModelProperty(value = "辨识ID")
    private String analyId;
    @ApiModelProperty(value = "分组组名")
    private String groupName;
    @ApiModelProperty(value = "辨识得分")
    private String score;
    @ApiModelProperty(value = "顺序")
    private Integer seqn;
    @ApiModelProperty(value = "是否主体质")
    private Boolean isMain;
    @ApiModelProperty(value = "是否兼体质")
    private Boolean isSub;
}