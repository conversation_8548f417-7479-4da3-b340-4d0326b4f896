package com.jiuzhekan.cbkj.beans.business.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserAnalysisResultAcu implements Serializable{

    @ApiModelProperty(value = "辨识ID")
    private String analyId;

    @ApiModelProperty(value = "穴位ID")
    private String acuId;

    @ApiModelProperty(value = "穴位代码")
    private String acuCode;

    @ApiModelProperty(value = "穴位名称")
    private String acuName;

    @ApiModelProperty(value = "穴位图片")
    private String acuImg;

    @ApiModelProperty(value = "穴位定位")
    private String acuPosition;


}
