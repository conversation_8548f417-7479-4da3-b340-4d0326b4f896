package com.jiuzhekan.cbkj.beans.statistics;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
@Data
public class TPrescriptionVO  implements Serializable {
    private String appId;
    private String insCode;
    private String preTimeBegin;
    private String preTimeEnd;
    private Integer preTolMoneyBegin;
    private Integer preTolMoneyEnd;
    private String preType;
    private String year;

    private String startYear;

    private String endYear;
    private List<String> insCodeList;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getInsCode() {
        return insCode;
    }

    public void setInsCode(String insCode) {
        this.insCode = insCode;
    }

    public String getPreTimeBegin() {
        return preTimeBegin;
    }

    public void setPreTimeBegin(String preTimeBegin) {
        this.preTimeBegin = preTimeBegin;
    }

    public String getPreTimeEnd() {
        return preTimeEnd;
    }

    public void setPreTimeEnd(String preTimeEnd) {
        this.preTimeEnd = preTimeEnd;
    }

    public Integer getPreTolMoneyBegin() {
        return preTolMoneyBegin;
    }

    public void setPreTolMoneyBegin(Integer preTolMoneyBegin) {
        this.preTolMoneyBegin = preTolMoneyBegin;
    }

    public Integer getPreTolMoneyEnd() {
        return preTolMoneyEnd;
    }

    public void setPreTolMoneyEnd(Integer preTolMoneyEnd) {
        this.preTolMoneyEnd = preTolMoneyEnd;
    }

    public String getPreType() {
        return preType;
    }

    public void setPreType(String preType) {
        this.preType = preType;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public List<String> getInsCodeList() {
        return insCodeList;
    }

    public void setInsCodeList(List<String> insCodeList) {
        this.insCodeList = insCodeList;
    }

}
