package com.jiuzhekan.cbkj.beans.business.setting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TBusinessEditionRead implements Serializable{

    @ApiModelProperty(value = "查看ID")
    private Integer readId;

    @ApiModelProperty(value = "版本ID")
    private String editionId;

    @ApiModelProperty(value = "版本号")
    private String editionNum;

    @ApiModelProperty(value = "用户ID")
    private String adminId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;


}
