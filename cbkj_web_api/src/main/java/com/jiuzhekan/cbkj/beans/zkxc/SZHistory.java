package com.jiuzhekan.cbkj.beans.zkxc;

import com.aspose.words.net.System.Data.DataException;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20 11:45
 * @Version 1.0
 */
@ApiModel
@Data
public class SZHistory {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    private String tongueOriginalImg;
    private Integer collectId;
    private String patientId;

    @ApiModelProperty(value = "舌色")
    private String lZhiStr;
    @ApiModelProperty(value = "舌尖")
    private String lJianStr;

    @ApiModelProperty(value = "舌形")
    private String psStr;


    @ApiModelProperty(value = "苔色")
    private String lCoatStr;

    @ApiModelProperty(value = "厚薄")
    private String hbStr;


    @ApiModelProperty(value = "腐腻")
    private String fnStr;

}
