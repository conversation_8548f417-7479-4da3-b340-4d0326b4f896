package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/12 13:52
 * @Version 1.0
 */
@Data
public class DigitPadFind {

    @ApiModelProperty("姓名|手机号|就诊卡号|就诊序号,其中一个。")
    private String searchKey;

    @ApiModelProperty("登记日期查询开始时间")
    private String startDate;
    @ApiModelProperty("登记日期查询结束时间")

    private String endDate;


    @ApiModelProperty(value = "医生id")
    private String doctorId;

    @ApiModelProperty(value = "科室id")
    private String deptId;

    @ApiModelProperty(value = "体格状态 0 未完成 1完成")
    private  Integer physiqueStatus;

    @ApiModelProperty(value = "预问诊 0 未完成 1完成")
    private  Integer diagnosisStatus;

    @ApiModelProperty(value = "病历复合 0 未符合 1 复合")
    private  Integer compositeMedical;

    @ApiModelProperty("中医四诊：-1. 未登记。待分配设备 0.已登记。 等待设备数据 1. 获取到设备数据")
    private  Integer infoStatus;

    @ApiModelProperty(value = "1.候诊中 2. 已完成")
    private  Integer dataStatus;

    @ApiModelProperty(value = "HIS就诊序号")
    private  String visitNo;

//    private String regNum;
}
