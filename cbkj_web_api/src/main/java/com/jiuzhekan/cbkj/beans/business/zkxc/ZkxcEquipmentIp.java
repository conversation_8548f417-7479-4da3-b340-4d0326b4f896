package com.jiuzhekan.cbkj.beans.business.zkxc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcEquipmentIp implements Serializable{

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "")
    private String macAddress;

    @ApiModelProperty(value = "转十位数字ip：INET_ATON('ip');转正常ipINET_NTOA('')")
    private String computerIp;

    @ApiModelProperty(value = "1.四诊仪2.经络仪3.热成像仪4.脉象复现仪")
    private Integer equipmentType;

    private String insCode;

}
