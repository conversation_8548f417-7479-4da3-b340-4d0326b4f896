package com.jiuzhekan.cbkj.beans.statistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsPrescriptionItem implements Serializable{

    @ApiModelProperty(value = "自增主键")
    private Integer id;

    @ApiModelProperty(value = "医联体ID")
    private String appId;

    @ApiModelProperty(value = "医联体")
    private String appName;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构")
    private String insName;

    @ApiModelProperty(value = "知识库药品id")
    private String matId;

    @ApiModelProperty(value = "知识库药品名称")
    private String matName;

    @ApiModelProperty(value = "HIS药品id")
    private String hisMatId;

    @ApiModelProperty(value = "HIS药品名称")
    private String hisMatName;

    @ApiModelProperty(value = "统计个数")
    private Integer matNumber;

    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入时间")
    private Date insertDate;


}
