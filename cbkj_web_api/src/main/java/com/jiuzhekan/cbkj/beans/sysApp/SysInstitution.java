package com.jiuzhekan.cbkj.beans.sysApp;

import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@ApiModel("医疗机构")
public class SysInstitution implements Serializable {

    @ApiModelProperty("医疗机构ID")
    private String insId;

    @ApiModelProperty("医疗机构代码")
    private String insCode;

    @ApiModelProperty("医疗机构名称")
    private String insName;

    @ApiModelProperty("上级医疗机构代码")
    private String insParentCode;

    @ApiModelProperty("上级医疗机构名称")
    private String insParentName;

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医联体名称")
    private String appName;

    @ApiModelProperty("医疗机构类别（1医院 2乡镇卫生院 3卫生站）")
    private String insCategory;

    @ApiModelProperty(value = "省代码 必填，便于后续统计")
    private String provinceCode;

    @ApiModelProperty(value = "省名称")
    private String provinceName;

    @ApiModelProperty(value = "城市代码 必填，便于后续统计")
    private String cityCode;

    @ApiModelProperty(value = "城名称")
    private String cityName;

    @ApiModelProperty(value = "区代码 必填，便于后续统计")
    private String areaCode;

    @ApiModelProperty(value = "区名称")
    private String areaName;

    @ApiModelProperty(value = "街代码")
    private String streetCode;

    @ApiModelProperty(value = "街名称")
    private String streetName;

    @ApiModelProperty(value = "详细地址")
    private String insAddress;

    @ApiModelProperty(value = "医疗机构等级，必填，便于后续统计" +
            "33 三级甲等 32 三级乙等 31 三级其他" +
            "23 二级甲等 22 二级乙等 21 二级其他" +
            "13 一级及其他")
    private String insLevel;

    @ApiModelProperty(value = "下级医疗机构列表", hidden = true)
    private List<SysInstitution> insList;

    @ApiModelProperty(value = "科室列表", hidden = true)
    private List<SysDepartment> deptList;

    public SysInstitution(String appId) {
        this.appId = appId;
    }

    public SysInstitution(String insCode, String appId) {
        this.insCode = insCode;
        this.appId = appId;
    }

    public SysInstitution(LinkedHashMap map) {
        this.appId = appId;
    }

}