package com.jiuzhekan.cbkj.beans.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/5 16:27
 * @Version 1.0
 */
@Data
@ApiModel
public class CueWordURLMatRequest {

    @ApiModelProperty(value = "药品名称")
    private String matName;

    @ApiModelProperty(value = "药品剂量")
    private String matDose;

    @ApiModelProperty(value = "包装单位")
    private String bzdw;

    @ApiModelProperty(value = "适宜技术-穴位名称")
    private String acuName;
    @ApiModelProperty(value = "适宜技术-针数")
    private String acuNum;
}
