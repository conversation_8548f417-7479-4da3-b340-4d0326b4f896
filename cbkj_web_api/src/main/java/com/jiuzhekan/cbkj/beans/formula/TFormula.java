package com.jiuzhekan.cbkj.beans.formula;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TFormula implements Serializable {

    @ApiModelProperty(value = "配方ID")
    private String formulaId;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "拥有者id")
    private String preOwner;

    @ApiModelProperty(value = "拥有者")
    private String preOwnerName;

    @NotEmpty(message = "方名不能为空")
    @ApiModelProperty(value = "方名")
    private String preName;

    @ApiModelProperty(value = "拼音")
    private String prePy;

    @ApiModelProperty(value = "五笔")
    private String preWb;

    @ApiModelProperty(value = "功效与适应症")
    private String efficacy;

    @NotEmpty(message = "配方类型不能为空")
    @ApiModelProperty(value = "配方类型（1为内服中草药，2为外用中草药）")
    private String preType;

    @NotEmpty(message = "药品剂型不能为空")
    @ApiModelProperty(value = "药品剂型（1散装饮片  2散装颗粒  3膏方 4小包装饮片 5小包装颗粒 ）")
    private String preMatType;

    @NotEmpty(message = "药房ID不能为空")
    @ApiModelProperty(value = "药房ID")
    private String storeId;

    @ApiModelProperty(value = "贴数（草药为贴数，熏蒸为总次数，适宜技术为天数）")
    private Short preNum;

    @ApiModelProperty(value = "服法ID")
    private String preDescriptionId;

    @ApiModelProperty(value = "服法")
    private String preDescription;

    @ApiModelProperty(value = "外用方式ID")
    private String preSmokeTypeId;

    @ApiModelProperty(value = "外用方式")
    private String preSmokeType;

    @ApiModelProperty(value = "熏蒸仪ID")
    private String preSmokeInstrumentId;

    @ApiModelProperty(value = "熏蒸仪")
    private String preSmokeInstrument;

    @ApiModelProperty(value = "每次几袋")
    private String preNBag;

    @ApiModelProperty(value = "每次几ml")
    private String preNMl;

    @ApiModelProperty(value = "每次几ml")
    private String preNMlName;

    @ApiModelProperty(value = "频次ID")
    private String preFrequencyId;

    @ApiModelProperty(value = "频次")
    private String preFrequency;

    @ApiModelProperty(value = "是否膏方（0否 1是）")
    private String isProduction;

    @ApiModelProperty(value = "煎药方式（1为代煎，0为自煎）")
    private String decoctType;

    @ApiModelProperty(value = "是否启用（0否 1是）")
    private String isUse;

    @ApiModelProperty(value = "开方时是否能修改（0否  1是）")
    private String isModify;

    @ApiModelProperty(value = "能否加药（0否  1是）")
    private String canAppend;

    @ApiModelProperty(value = "药房价格ID")
    private String storePriceId;

    @ApiModelProperty(value = "单剂金额")
    private BigDecimal preSingleMoney;

    @ApiModelProperty(value = "配方总金额")
    private BigDecimal preTolMoney;

    @ApiModelProperty(value = "排序")
    private Integer preOrder;

    @ApiModelProperty(value = "新增时间")
    private Date createDate;

    @ApiModelProperty(value = "新增人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "删除时间")
    private Date delDate;

    @ApiModelProperty(value = "删除人")
    private String delUser;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private String isDel;

    @ApiModelProperty(value = "中药信息")
    private String preItem;

    @ApiModelProperty(value = "库存")
    private BigDecimal preStock;

    @NotNull
    @ApiModelProperty(value = "配方明细")
    private List<TFormulaItem> itemList;

    /**
     * HIS药品目录（查询用）
     */
    private String ypmlHis;

    //费别  1医保  0自费
//    private String isMedical;

    @ApiModelProperty(value = "付费方式")
    private String payType;

    @ApiModelProperty(value = "文件夹ID")
    private String folderId;

    public TFormula(String formulaId) {
        this.formulaId = formulaId;
    }

    public TFormula(String preName, String preType, String storeId,String folderId) {
        this.preName = preName;
        this.preType = preType;
        this.storeId = storeId;
        this.folderId = folderId;
    }
}
