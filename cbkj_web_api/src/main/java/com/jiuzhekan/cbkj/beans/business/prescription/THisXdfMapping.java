package com.jiuzhekan.cbkj.beans.business.prescription;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class THisXdfMapping implements Serializable{

    @ApiModelProperty(value = "HIS-协定方序号")
    private String xh;

    @ApiModelProperty(value = "搜索条件")
    private String searchStr;

    @ApiModelProperty(value = "")
    private String persPreId;


}
