package com.jiuzhekan.cbkj.beans.business.zkxcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class ZkxcCollectInfo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer collectId;

    @ApiModelProperty(value = "医⽣账户名称")
    private String accountName;

    @ApiModelProperty(value = "患者年龄")
    private Integer age;

    @ApiModelProperty(value = "血压")
    private String bloodPressure;

    @ApiModelProperty(value = "血糖")
    private String bloodSugar;

    @ApiModelProperty(value = "")
    private String cardNo;

    @ApiModelProperty(value = "四诊仪返回的时间")
    private Date createTime;

    @ApiModelProperty(value = "医生名字")
    private String doctorName;

    @ApiModelProperty(value = "患者身高")
    private String height;

    @ApiModelProperty(value = "")
    private String uuid;

    @ApiModelProperty(value = "四诊仪器的mac地址，注意区分")
    private String mac;

    @ApiModelProperty(value = "医⽣所属机构")
    private String org;

    @ApiModelProperty(value = "患者电话")
    private String phone;

    @ApiModelProperty(value = "患者姓名")
    private String realName;

    @ApiModelProperty(value = "患者性别0女1男")
    private String sex;

    @ApiModelProperty(value = "患者体重")
    private String weight;

    @ApiModelProperty(value = "挂号id")
    private String registerId;

    @ApiModelProperty(value = "患者信息id")
    private String patientId;

    @ApiModelProperty(value = "pdf文件路径")
    private String pdfPath;

    @ApiModelProperty(value = "zip文件路径")
    private String mzFile;

    @ApiModelProperty(value = "系统插入时间-多条挂号id一样，取最新条")
    private Date insertTime;
    @ApiModelProperty(value = "默认机构脉复仪的mac地址")
    private String defaultMaiFuMacAddress;

    @ApiModelProperty(value = "整体脉象结果")
    private String pulsePotentialFinal;
    @ApiModelProperty(value = "左右手寸关尺信息")
    private List<ZkxcChiCunGuan> zkxcChiCunGuanList;
}
