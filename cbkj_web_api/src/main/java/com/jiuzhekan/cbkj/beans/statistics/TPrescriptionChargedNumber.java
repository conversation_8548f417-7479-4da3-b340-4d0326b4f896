package com.jiuzhekan.cbkj.beans.statistics;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <p>
 * 统计医生收费处方数量表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-16
 */
@Data
public class TPrescriptionChargedNumber implements Serializable {

    private String id;

    /**
     * 开方时间
     */
//@JsonFormat(pattern = "yyyy-MM-dd" ,timezone = "GMT+8")
    private Date preTime;

    private String appId;

    private String insCode;
    /**
     * 医疗机构id
     */

    private String insName;

    /**
     * 科室类型
     */

    private String preMzZy;

    /**
     * 科室名称
     */

    private String deptName;

    /**
     * 开方医生
     */

    private String preDoctorname;

    /**
     * 已收费处方数量
     */

    private Integer preChargedNumber;


}
