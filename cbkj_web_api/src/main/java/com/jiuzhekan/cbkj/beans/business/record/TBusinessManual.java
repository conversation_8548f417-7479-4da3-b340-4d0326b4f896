package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.beans.business.setting.TBusinessAnnex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "操作手册接收类" , description = "操作手册类")
public class TBusinessManual implements Serializable{

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "编号")
    private Integer manualNum;

    @ApiModelProperty(value = "标题")
    private String manualTitle;

    @ApiModelProperty(value = "下载次数")
    private Integer manualDownTimes;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者ID")
    private String createUserId;

    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改者ID")
    private String updateUserId;

    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

    @ApiModelProperty(value = "删除者ID")
    private String deleteUserId;

    @ApiModelProperty(value = "状态0启用，1禁用")
    private Byte manualState;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private Byte isDel;

    /**
     * @Description :
     * <AUTHOR> xhq
     * @updateTime : 2020/2/27 17:43
     */
    @ApiModelProperty(value = "附件集合")
    private List<TBusinessAnnex> annexList;
}