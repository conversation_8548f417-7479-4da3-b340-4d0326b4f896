package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.beans.business.prescription.TPreDecoction;
import com.jiuzhekan.cbkj.beans.business.prescription.TPreExpress;
import com.jiuzhekan.cbkj.beans.business.prescription.TPreProduction;
import com.jiuzhekan.cbkj.beans.business.record.*;
import com.jiuzhekan.cbkj.beans.dosageform.TPrescriptionPreparation;
import com.jiuzhekan.cbkj.beans.validRecordBySDK.EvaluationResponse;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "处方表")
public class TPreRespVO implements Serializable {
    @ApiModelProperty(value = "处方ID")
    private String preId;
    @ApiModelProperty(value = "处方号码")
    private String preNo;
    @ApiModelProperty(value = "病历ID")
    private String recId;
    @ApiModelProperty(value = "")
    private String appId;
    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;
    @ApiModelProperty(value = "患者ID")
    private String patientId;
    @ApiModelProperty(value = "开方医生ID")
    private String preDoctor;
    @ApiModelProperty(value = "开方医生姓名")
    private String preDoctorname;
    @ApiModelProperty(value = "开方时间")
    private Date preTime;
    @ApiModelProperty(value = "开方时间")
    private String preTimeStr;
    @ApiModelProperty(value = "单剂金额")
    private String preSingleMoney;
    @ApiModelProperty(value = "贴数（草药为贴数，熏蒸为总次数，适宜技术为天数）")
    private Short preNum;
    @ApiModelProperty(value = "门诊住院标志（1门诊 2住院）")
    private Short preMzZy;
    @ApiModelProperty(value = "出院带药标志(1是，0否)")
    private Short dischargeMedication;
    @ApiModelProperty(value = "处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方 5中药制剂）")
    private String preType;
    @ApiModelProperty(value = "药品剂型（1散装饮片  2散装颗粒 3膏方 4小包装饮片 5小包装颗粒  6配方 7制剂）")
    private String preMatType;
    @ApiModelProperty(value = "用法")
    private String preUsage;
    @ApiModelProperty(value = "服法ID")
    private String preDescriptionId;
    @ApiModelProperty(value = "服法")
    private String preDescription;
    //膏方有无糖-公用代码
    @ApiModelProperty(value = "膏方有无糖")
    private String productionType;
    @ApiModelProperty(value = "膏方有无糖ID")
    private String productionTypeId;
    @ApiModelProperty(value = "频次ID")
    private String preFrequencyId;
    @ApiModelProperty(value = "频次")
    private String preFrequency;
    @ApiModelProperty(value = "医嘱")
    private String preAdvice;
    @ApiModelProperty(value = "0-空白方，1-智能推方（证型方剂处方），2-方剂检索（经方），3-协定方" +
            "，4-证型推倒，5-验案转方，6-病历历史记录转方，7-国医大师机器人")
    private String preOrigin;
    @ApiModelProperty(value = "来源处方ID")
    private String preOriginId;

    private String preOriginXh;
    @ApiModelProperty(value = "来源处方名")
    private String preOriginName;
    @ApiModelProperty(value = "来源处方能否修改")
    private Boolean preOriginUpdate = true;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private String isDel;
    @ApiModelProperty(value = "审核类型（0为自动审核，1为人工审核）")
    private String checkType;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "审核不通过原因")
    private String checkAdvise;
    @ApiModelProperty(value = "审核人ID")
    private String checkUserid;
    @ApiModelProperty(value = "审核人姓名")
    private String checkUsername;
    @ApiModelProperty(value = "审核时间")
    private Date checkTime;
    @ApiModelProperty(value = "收退费状态（0未收费 1已收费 2已退费）")
    private String isPay;
    @ApiModelProperty(value = "收退费时间")
    private Date payTime;
    @ApiModelProperty(value = "收退费人ID")
    private String payUserid;
    @ApiModelProperty(value = "收退费人姓名")
    private String payUsername;
    @ApiModelProperty(value = "打印次数")
    private Short isPrint;
    @ApiModelProperty(value = "药品总金额")
    private String matTolMoney;
    @ApiModelProperty(value = "处方总金额")
    private String preTolMoney;
    @ApiModelProperty(value = "处方实收总金额")
    private String preTolMoneyReal;
    @ApiModelProperty(value = "取药方式（1配送，0自提）")
    private String dcType;
    @ApiModelProperty(value = "配送类型（0.定点配送点1.配送到家）")
    private String dcTypeSubclass;
    @ApiModelProperty(value = "定点配送id")
    private String selfPickupId;
    @ApiModelProperty(value = "煎药方式（1为代煎，0为自煎）")
    private String decoctType;
    @ApiModelProperty(value = "是否膏方（0否 1是）")
    private String isProduction;
    @ApiModelProperty(value = "HIS处方ID")
    private String hisPreId;
    @ApiModelProperty(value = "代煎费")
    private String preDecoctionFee;
    @ApiModelProperty(value = "制膏费")
    private String preProductionFee;
    @ApiModelProperty(value = "配送费")
    private String preExpressFee;
    @ApiModelProperty(value = "治疗费")
    private String preSmoMoney;
    @ApiModelProperty(value = "特殊调配费")
    private String preSpecialFee;
    @ApiModelProperty(value = "处方状态（-1为创建，0为已审核，1为已支付，2为已配药，3为已发药，4为已退药）")
    private String recExtType;
    @ApiModelProperty(value = "药房ID")
    private String storeId;
    @ApiModelProperty(value = "药房名称")
    private String storeName;
    @ApiModelProperty(value = "配送ID")
    private String dcId;
    @ApiModelProperty(value = "适宜技术类型ID", required = true)
    private String acuTypeId;
    @ApiModelProperty(value = "适宜技术类型代码（1针灸，2艾灸，3推拿，4拔罐）",position = 0)
    private String acuType;
    @ApiModelProperty(value = "针灸项目ID", required = true)
    private String acuProjectId;
    @ApiModelProperty(value = "针灸项目（1银针 2电针）",position = 1)
    private String acuProject;
    @ApiModelProperty(value = "收费项目编码【001,002】逗号分隔")
    private String payCode;
    @ApiModelProperty(value = "操作指南")
    private String acuOperation;
    @ApiModelProperty(value = "原处方ID（退费处方要填原处方ID）")
    private String originPreId;
    @ApiModelProperty(value = "兼证ID")
    private String preSchemeaddid;

    @ApiModelProperty(value = "服药时间ID")
    private String preUsetimeId;
    @ApiModelProperty(value = "服药时间说明")
    private String preUsetimeDes;
    @ApiModelProperty(value = "每次几ml")
    private String preNMl;
    @ApiModelProperty(value = "每次几ml")
    private String preNMlName;
    @ApiModelProperty(value = "每次几袋")
    private String preNBag;
    @ApiModelProperty(value = "熏蒸仪ID")
    private String preSmokeInstrumentId;
    @ApiModelProperty(value = "熏蒸仪（A类 B类）")
    private String preSmokeInstrument;
    @ApiModelProperty(value = "是否院内治疗（1是 0否）")
    private String isHospitalTreatment;

    @ApiModelProperty(value = "是否医保（0自付 1医保）")
    private String isInsurance;
    @ApiModelProperty(value = "代煎贴数")
    private Short preDecoctNum;

    @ApiModelProperty(value = "外用方式ID")
    private String preSmokeTypeId;
    @ApiModelProperty(value = "外用方式")
    private String preSmokeType;

    /**
     * 2020-10-13安吉增加特病
     */
    @ApiModelProperty("特病标志（0否 1是）")
    private String isSpecialDis;
    @ApiModelProperty("特病名称")
    private String specialName;
    @ApiModelProperty("特病代码")
    private String specialCode;

    @ApiModelProperty(value = "修改时旧处方号")
    private String preOldNo;
    @ApiModelProperty(value = "住院医嘱类型（1临嘱，2长嘱）")
    private String preAdviceType;
    @ApiModelProperty(value = "医嘱时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String preAdviceTime;

    @ApiModelProperty(value = "开始时间")
    private String beganTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;


    @TianAnDecryptField
    @ApiModelProperty(value = "收货人(配送表)")
    private String dcName;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人手机号(配送表)")
    private String dcMobile;

    @TianAnDecryptField
    @ApiModelProperty(value = "收货人详细地址(配送表)")
    private String dcAddress;

    @TianAnDecryptField
    @ApiModelProperty(value = "省(配送表)")
    private String dcCounty;

    @TianAnDecryptField
    @ApiModelProperty(value = "市(配送表)")
    private String dcTown;

    @TianAnDecryptField
    @ApiModelProperty(value = "区(配送表)")
    private String dcVillage;

    @TianAnDecryptField
    @ApiModelProperty(value = "街道(配送表)")
    private String dcStreet;

    @ApiModelProperty(value = "省代码(配送表)")
    private String dcCountyCode;
    @ApiModelProperty(value = "市代码(配送表)")
    private String dcTownCode;
    @ApiModelProperty(value = "区代码(配送表)")
    private String dcVillageCode;
    @ApiModelProperty(value = "街道代码(配送表)")
    private String dcStreetCode;
    @ApiModelProperty(value = "快递公司(配送表)")
    private String dcCompany;
    @ApiModelProperty(value = "快递单号(配送表)")
    private String dcNumber;
    @ApiModelProperty(value = "是否发货（0未发货 1已发货）(配送表)")
    private String isDeliver;


    @ApiModelProperty(value = "处方测评ID(处方测评表)")
    private String evaId;

    @ApiModelProperty(value = "处方测评类型（1安全用药测评  2经方相似度测评）(处方测评表)")
    private String evaType;

    @ApiModelProperty(value = "慎禁忌(处方测评表)", required = true)
    private String evaSjj;

    @ApiModelProperty(value = "孕妇慎禁忌(处方测评表)", required = true)
    private String evaYfsjj;

    @ApiModelProperty(value = "十八反(处方测评表)", required = true)
    private String evaSbf;

    @ApiModelProperty(value = "十九畏(处方测评表)", required = true)
    private String evaSjw;

    @ApiModelProperty(value = "用药不宜(处方测评表)", required = true)
    private String evaBy;

    @ApiModelProperty(value = "服药饮食禁忌(处方测评表)", required = true)
    private String evaYsjj;

    @ApiModelProperty(value = "药物毒性说明(处方测评表)", required = true)
    private String evaDxsm;

    @ApiModelProperty(value = "病证用药禁忌(处方测评表)", required = true)
    private String evaBzjj;

    @ApiModelProperty(value = "剂量超标(处方测评表)", required = true)
    private String evaJlcb;

    @ApiModelProperty(value = "计量偏低", required = true)
    private String evaJlpd;

    @ApiModelProperty(value = "计量超规定", required = true)
    private String evaJlcgd;

    private String patientTypes;

    @ApiModelProperty(value = "草药处方明细集合(内服,外用,中成药)(处方测评表)")
    private List<TPrescriptionItem> itemList;

    @ApiModelProperty(value = "适宜技术处方明细集合")
    private List<TPrescriptionAcuItem> acuItemList;

    @ApiModelProperty(value = "订单状态集合")
    private List<TOrderStatus> orderList;

    @ApiModelProperty(value = "审核历史集合")
    private List<TPrescriptionExamine> examineList;

    @ApiModelProperty(value = "中药制剂明细集合", required = true)
    private List<TPrescriptionPreparationItem> preparationItemList;

    /*****代煎*****/
    @ApiModelProperty(value = "处方代煎信息")
    private TPreDecoction decoction;
    /*****制膏*****/
    @ApiModelProperty(value = "处方制膏信息")
    private TPreProduction production;
    /*****配送*****/
    @ApiModelProperty(value = "处方配送信息")
    private TPreExpress express;

    private EvaluationResponse preSafetyEvaluationSDK;
    /**
     * 前端需要当前服务器时间和处方时间的毫秒数差值
     */
    private long preTimeOverdue;

    @ApiModelProperty(value = "制剂信息-V6.1.0新增对象.没开制剂情况下，请传null")
    private TPrescriptionPreparation preparation;

    @ApiModelProperty(value = "是否加急0 不加急 1加急-V6.1.0新增")
    private Integer urgentSignValue;

    @ApiModelProperty(value = "出院带药(1是，0否)", required = true)
    private String dischargeMedicationMark;
}