package com.jiuzhekan.cbkj.beans.business.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@ApiModel("药房费用配置信息表")
public class TDisplayMoneySetting implements Serializable {

    private String id;

    @ApiModelProperty("配置ID")
    private String setId;

    @ApiModelProperty("门诊住院（1门诊，2住院）")
    private String outpatientOrHospitalization;

    @ApiModelProperty("代煎，制膏，配送（1代煎，2制膏，3配送）")
    private String decoctionOrProductionExpress;

    @ApiModelProperty("通用，配方（1通用，2配方）")
    private String currencyOrFormula;

    @ApiModelProperty("字典ID")
    private String dicId;

    @ApiModelProperty("字典代码")
    private String dicCode;

    @ApiModelProperty("字典名称")
    private String dicName;

    @ApiModelProperty("是否默认（1是0否）")
    private String isDefault;

    @ApiModelProperty("收费项目配置是否显示(0显示,1不显示)")
    private String isShowSetting;

    @ApiModelProperty("HIS收费代码")
    private String chargeCode;

    @ApiModelProperty("收费项目名称")
    private String chargeName;

    @ApiModelProperty("价格")
    private BigDecimal price;

    public TDisplayMoneySetting(String dicId, String dicCode, String dicName, BigDecimal price) {
        this.dicId = dicId;
        this.dicCode = dicCode;
        this.dicName = dicName;
        this.price = price;
    }
}
