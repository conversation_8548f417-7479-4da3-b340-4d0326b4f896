package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetHisP {
    private String appId;
    private String insCode;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("患者性别（男M女F）")
    private String patientGender;

    @ApiModelProperty("患者出生日期(格式：2020-02-24)")
    private String patientBirthday;

    @ApiModelProperty("患者证件号码")
    private String patientCertificate;

    @ApiModelProperty("患者证件类型（01居民身份证，02居民户口簿，03护照，04军官证（士兵证）05驾驶执照，06港澳居民来往内地通行证，07台湾居民来往内地通行证，99其他）")
    private String patientCertificateType;

    @ApiModelProperty("患者手机号")
    private String patientMobile;

    @ApiModelProperty("患者就诊卡号")
    private String medicalCardNo;

    @ApiModelProperty("患者就诊序号")
    private String visitNo;

    @ApiModelProperty("HIS患者ID")
    private String originPatientId;
}
