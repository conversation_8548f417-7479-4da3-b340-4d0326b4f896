package com.jiuzhekan.cbkj.beans.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TAnalysisNationalIndexVo implements Serializable {

    @ApiModelProperty(value = "医联体ID")
    private String appId;
    @ApiModelProperty(value = "医疗机构编码")
    private String insCode;
    @ApiModelProperty(value = "医生ID")
    private String doctorId;
    @ApiModelProperty(value = "医生姓名")
    private String doctorName;
    @ApiModelProperty(value = "科室ID")
    private String deptId;
    @ApiModelProperty(value = "科室名称")
    private String deptName;
    @ApiModelProperty(value = "门诊中药处方数")
    private String prescriptionNum;
    @ApiModelProperty(value = "门诊中药处方比例")
    private Double prescriptionPro;
    @ApiModelProperty(value = "门诊中药处方目标比例")
    @Value(value = "0")
    private Double prescriptionProportion;
    @ApiModelProperty(value = "门诊散装中药饮片和小包装中药饮片处方数")
    private String preSZOrXBZNum;
    @ApiModelProperty(value = "门诊散装中药饮片和小包装中药饮片处方比例")
    private Double preSZOrXBZPro;
    @ApiModelProperty(value = "门诊散装中药饮片和小包装中药饮片处方目标比例")
    @Value(value = "0")
    private Double preSZOrXBZProportion;
    @ApiModelProperty(value = "门诊患者中药饮片使用人次")
    private String peopleUseNum;
    @ApiModelProperty(value = "门诊患者中药饮片使用率")
    private Double peopleUsePro;
    @ApiModelProperty(value = "门诊患者中药饮片目标使用率")
    @Value(value = "0")
    private Double peopleUseProportion;
    @ApiModelProperty(value = "门诊患者使用中医非药物疗法人次")
    private String peopleNonUseNum;
    @ApiModelProperty(value = "门诊患者使用中医非药物疗法比例")
    private Double peopleNonUsePro;
    @ApiModelProperty(value = "门诊患者使用中医非药物疗法目标比例")
    @Value(value = "0")
    private Double peopleNonUseProportion;
    @ApiModelProperty(value = "中药收入")
    private String CMedicineIncome;
    @ApiModelProperty(value = "中药收入占药品收入比例")
    private Double CMedicineIncomePro;
    @ApiModelProperty(value = "中药收入占药品收入目标比例")
    @Value(value = "0")
    private Double CMedicineIncomeProportion;
    @ApiModelProperty(value = "中药饮片收入")
    private String CMedicinePiecesIncome;
    @ApiModelProperty(value = "中药饮片收入占药品收入比例")
    private Double CMedicinePiecesIncomePro;
    @ApiModelProperty(value = "中药饮片收入占药品收入目标比例")
    @Value(value = "0")
    private Double CMedicinePiecesIncomeProportion;
    @ApiModelProperty(value = "开始时间")
    private String startTimeStr;
    @ApiModelProperty(value = "结束时间")
    private String endTimeStr;
    @ApiModelProperty(value = "导出类别 1、按金额 2、按科室占比 4、按医生占比 3、按科室目标占比")
    private String exportCategory;
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    @ApiModelProperty(value = "创建人")
    private String createUser;
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "页码")
    private Integer page;
    @ApiModelProperty(value = "每页数量")
    private Integer limit;

    @ApiModelProperty(value = "国考列表形成导出表格使用数据")
    private List<TAnalysisNationalIndexVo> list;
}
