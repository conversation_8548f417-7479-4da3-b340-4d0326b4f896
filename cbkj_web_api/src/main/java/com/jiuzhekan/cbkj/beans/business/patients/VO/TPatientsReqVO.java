package com.jiuzhekan.cbkj.beans.business.patients.VO;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * Created By xhq on  2019/12/23 13:21
 */
@ApiModel("患者信息请求条件类")
@Data
public class TPatientsReqVO implements Serializable {
    private String orderName;     // 病人信息查询(0 病人姓名,1 就诊卡号,2 病人手机号,3 病人身份证号)
    private String orderValue;    // 输入框内容
    /* @ApiModelProperty(value = "患者ID")
     private String patientId;*/
    private String appId;
    private String insCode;
    private String docId;
    private String disId;
    private String disName;


    @TianAnEncryptField
    private String patientName;
    private String patientGender;

    @TianAnDecryptField
    private String patientCertificate;
    @TianAnEncryptField
    @TianAnDecryptField
    private String patientMobile;

    private String patientPy;
    private String patientWb;
    private String medicalCardNo;
    private String originPatientId;
    private String patientBirthday;
    private String visitNo;

    @ApiModelProperty(hidden = true)
    private String patientVisitRange;


    public void initOrderParams(){
        if (StringUtils.isNotBlank(this.getOrderName())) {
            if (Constant.BASIC_STRING_ZERO.equals(this.getOrderName())) {
                this.setPatientName(this.getOrderValue());
            } else if (Constant.BASIC_STRING_ONE.equals(this.getOrderName())) {
                this.setMedicalCardNo(this.getOrderValue());
            } else if (Constant.BASIC_STRING_TWO.equals(this.getOrderName())) {
                this.setPatientMobile(this.getOrderValue());
            } else if (Constant.BASIC_STRING_THREE.equals(this.getOrderName())) {
                this.setPatientCertificate(this.getOrderValue());
            } else if (Constant.BASIC_STRING_FOUR.equals(this.getOrderName())) {
                this.setVisitNo(this.getOrderValue());
            }
        }
    }
}