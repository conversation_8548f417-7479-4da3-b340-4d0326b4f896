package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/31 16:28
 * @Version 1.0
 */
@Data
@ApiModel
public class BsVillage  implements Serializable {
    @ApiModelProperty(value = "行政村主键id")
    private Integer bsVillage;
    @ApiModelProperty(value = "行政村代码")
    private String villageCode;
    @ApiModelProperty(value = "街道代码")
    private String streetCode;
    @ApiModelProperty(value = "行政村全称名称")
    private String villageName;
    @ApiModelProperty(value = "行政村简写名称")
    private String shortName;

    private String sort;
    private String dataState;
}
