package com.jiuzhekan.cbkj.beans.business.record.preCheckVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * CheckPreVO
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/9/8
 */
@Data
@ApiModel(value = "审核处方")
public class CheckPreVO implements Serializable {
    @ApiModelProperty(value = "处方ID")
    private String preId;
    @ApiModelProperty(value = "处方号")
    private String preNo;
    @ApiModelProperty(value = "处方类型")
    private String preType;
    @ApiModelProperty(value = "处方时间")
    private String preTime;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "审核类型（0为自动审核，1为人工审核，2下班自动通过）")
    private String checkType;
    @ApiModelProperty(value = "审核不通过原因")
    private String checkAdvise;
}
