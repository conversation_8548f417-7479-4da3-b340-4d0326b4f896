package com.jiuzhekan.cbkj.beans.business.record.preCheckVO;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "处方审核请求参数类")
public class PreCheckReqVO implements Serializable {
    @ApiModelProperty(value = "患者姓名,后台会判断中英文")
    private String recName;
    @ApiModelProperty(value = "开始时间('2019-10-01 00:00:00')")
    private String startDate;
    @ApiModelProperty(value = "结束时间")
    private String endDate;
    @ApiModelProperty(value = "审核状态（0未审核 1审核通过 2审核未通过）")
    private String isCheck;
    @ApiModelProperty(value = "审核类型（0为自动审核，1为人工审核）")
    private String checkType;
    @ApiModelProperty(value = "病名")
    private String disName;
    @ApiModelProperty(value = "处方状态")
    private String recType;
    @ApiModelProperty(value = "处方筛选条件（0病人姓名，1就诊卡号，2联系方式，3身份证号）")
    private String orderName;
    private String orderValue;

    @TianAnEncryptField
    private String patientName;
    @TianAnEncryptField
    private String patientMobile;

    @TianAnEncryptField
    private String patientCertificate;

    private String patientPy;
    private String patientWb;

    private String appId;
    private String insCode;
    private String docId;

    private String isPreCheck;
    @ApiModelProperty(value = "处方审核状态（-1未审核，0未通过，1待支付，2已支付）")
    private String preStatus;


    private List<Integer> tableHistoryYears;

}