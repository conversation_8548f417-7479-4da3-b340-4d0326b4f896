package com.jiuzhekan.cbkj.beans.statistics;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * @program: pre_api
 * @description: 返回给前端的处方数据对象
 * @author: wangtao
 * @create: 2021-03-16 14:14
 **/
@Data
@NoArgsConstructor
public class TPreChargedNumVO  implements Serializable {

    /**
     * 医疗机构id
     */

    private String insName;

    /**
     * 科室类型
     */

    private String preMzZy;

    /**
     * 科室名称
     */

    private String deptName;

    /**
     * 开方医生
     */

    private String preDoctorname;

    /**
     * 已收费处方数量
     */

    private Integer preChargedNumber;

    /**
     * 合计
     */
    private String count;

    public TPreChargedNumVO(TPrescriptionChargedNumber tPreNum) {
        this.insName = tPreNum.getInsName();
        this.preMzZy = tPreNum.getPreMzZy();
        this.deptName = tPreNum.getDeptName();
        this.preDoctorname = tPreNum.getPreDoctorname();
        this.preChargedNumber = tPreNum.getPreChargedNumber();
    }
}
