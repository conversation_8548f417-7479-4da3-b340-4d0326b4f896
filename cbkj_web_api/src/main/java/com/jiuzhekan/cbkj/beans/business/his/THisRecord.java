package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * his记录
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020年07月13日 10:20:00
 */
@ApiModel
@Data
public class THisRecord implements Serializable {

    @ApiModelProperty("TOKEN")
    private String token;

    @ApiModelProperty("医联体")
    private String appId;

    @ApiModelProperty("医疗机构")
    private String insCode;

    @ApiModelProperty("时间戳")
    private String timestamp;

    @ApiModelProperty("主诉")
    private String patientContent;

    @ApiModelProperty("现病史")
    private String nowDesc;

    @ApiModelProperty("既往史")
    private String pastDesc;

    @ApiModelProperty("中医四诊")
    private String fourDiagnosis;

    @ApiModelProperty("体格检查")
    private String physical;

    @ApiModelProperty("辅助检查")
    private String auxiliaryExam;

    @ApiModelProperty("医生ID")
    private String adminInfoId;

    @ApiModelProperty("患者ID")
    private String patientsId;

    @ApiModelProperty("疾病代码")
    private String disCode;

    @ApiModelProperty("疾病名称")
    private String disName;

    @ApiModelProperty("证型代码")
    private String symCode;

    @ApiModelProperty("证型名称")
    private String symName;

    @ApiModelProperty("西医诊断")
    private String westernDisease;

    @ApiModelProperty("西医病名编码（ICD-10编码）")
    private String westernCode;

    @ApiModelProperty("门诊住院标志（1门诊 2住院）")
    private String preMzZy;

    @ApiModelProperty("门诊号/住院号")
    private String hospitalNo;

    @ApiModelProperty("科室")
    private String deptId;

    @ApiModelProperty("科室")
    private String dept;

    @ApiModelProperty("病区")
    private String wardId;

    @ApiModelProperty("病区")
    private String ward;

    @ApiModelProperty("床位号")
    private String bedNo;

    @ApiModelProperty("扩展信息，获取建议处方时返回")
    private String extendedTxt;

    @ApiModelProperty(value = "HIS诊序号")
    private String visitNo;

    @ApiModelProperty(value = "是否自费（0否 1是）")
    private String isOwnExp;

    @ApiModelProperty(value = "特病标志（0否 1是）")
    private String isSpecialDis;

    @ApiModelProperty(value = "特病编码")
    private String specialDisCode;

    @ApiModelProperty(value = "代煎费收费码")
    private String preDecoctionFeeCode;

    @ApiModelProperty(value = "治疗费收费码")
    private String preProductionFeeCode;

    @ApiModelProperty(value = "配送费收费码")
    private String preExpressFeeCode;

    @ApiModelProperty(value = "制膏费收费码")
    private String preSmoMoneyCode;

    @ApiModelProperty(value = "入科时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String admissionTime;

    @ApiModelProperty(value = "互联网标志，1是0否")
    private Integer isInternet;

    @ApiModelProperty(value = "HIS协定方序号")
    private String xdfxh;
    private String patientWeight;
    private String patientHeight;
    private String treatmentAdvice;
    private String grs;
    private String gms;
}