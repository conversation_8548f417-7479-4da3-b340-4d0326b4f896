package com.jiuzhekan.cbkj.beans.business.sysCode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "系统代码")
public class TSysCode implements Serializable {
    @ApiModelProperty(value = "大类ID")
    private Short codeId;
    @ApiModelProperty(value = "大类代码")
    private String codeValue;
    @ApiModelProperty(value = "大类名称")
    private String codeName;
    @ApiModelProperty(value = "序号")
    private Short codeNum;
    private Date createDate;
    private String createUser;
    private String createUsername;
    private Date updateDate;
    private String updateUser;
    private String updateUsername;
    private Date delDate;
    private String delUser;
    private String delUsername;
    @ApiModelProperty(value = "是否删除（0否 1是)")
    private String isDel;
    @ApiModelProperty(value = "禁用时间")
    private Date disableDate;
    @ApiModelProperty(value = "禁用人")
    private String disableUser;
    @ApiModelProperty(value = "禁用人姓名")
    private String disableUsername;
    @ApiModelProperty(value = "是否禁用  1：禁用  0：启用")
    private String isDisable;
    @ApiModelProperty(value = "明细")
    private List<Map<String, Object>> itemList;

    private String codeDicId;
}