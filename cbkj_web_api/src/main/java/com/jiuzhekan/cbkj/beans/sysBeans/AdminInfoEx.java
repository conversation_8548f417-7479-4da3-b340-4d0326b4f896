package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * AdminInfoEx
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/10
 */
@Data
@NoArgsConstructor
@ApiModel
public class AdminInfoEx implements Serializable{


    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "扩展文字: 权限(私有 科室 医疗机构 医共体)")
    private String userExText;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "输入码(1拼音 2五笔)")
    private String shuruma;

    @ApiModelProperty(value = "输入码(1拼音 2五笔)")
    private String inputCode;

    @ApiModelProperty(value = "介绍")
    private String introduce;

    @ApiModelProperty(value = "中医资质 （1有0无）")
    private String isQualifier;

    @ApiModelProperty(value = "协定方分享权限(0.个人1.科室2全院) 多个用逗号分隔")
    private String prescriptionShare;

    @ApiModelProperty(value = "病历模板分享权限(0私有 1科室 2医疗机构 3医共体) 多个用逗号分隔")
    private String descriptionShare;

    @ApiModelProperty(value = "默认处方类型(1内服 2外用 4适宜技术 5中药制剂) 多个用逗号分隔")
    private String defaultPrescriptionShare;

    @ApiModelProperty(value = "用户习惯内服（1:表示一行一列,2:表示一行2列）")
    private String userHabitOral;

    @ApiModelProperty(value = "用户习惯外用（1:表示一行一列,2:表示一行2列）")
    private String userHabitOutward;

    @ApiModelProperty(value = "用户习惯适宜技术（1:表示一行一列,2:表示一行2列）")
    private String userHabitAppropriate;

    @ApiModelProperty(value = "一个月内不提醒剂量不能整除(1四舍五入2自己填)")
    private String oneMonthNoDivide;

    @ApiModelProperty(value = "特殊药品的使用权限(1有 0无)超安全用药权限")
    private String specialDrugsUsePermission;


}
