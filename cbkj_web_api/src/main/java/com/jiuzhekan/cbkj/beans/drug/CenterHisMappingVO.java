package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019年07月25日 13:40:00
 */

@Data
@ApiModel
@NoArgsConstructor
public class CenterHisMappingVO implements Serializable {
    /**
     * 新华需求：药品剂量，一个药需要有多规格：5.21.9 ： http://121.41.90.252:8080/zentao/story-view-2771.html
     */
    private List<String> matDoseList;
    @ApiModelProperty("唯一标识")
    private String uniqueId;

    @ApiModelProperty("统一代码")
    private String yaopindmTy;

    @ApiModelProperty("中心药房药品目录ID")
    private String centerYpmlId;

    @ApiModelProperty("中心药房药品代码")
    private String centerYpdm;

    @ApiModelProperty("中心药房药品名称")
    private String centerYpmc;

    @ApiModelProperty("中心药房药品规格ID")
    private String centerGgid;

    @ApiModelProperty("中心药房药品规格")
    private String centerYpgg;

    @ApiModelProperty("中心药房药品单位")
    private String centerYpdw;

    @ApiModelProperty("中心药房药品产地")
    private String centerYpcd;

    @ApiModelProperty("中心药房药品产地ID")
    private String cdidCenter;

    @ApiModelProperty("中心药房药品单价")
    private BigDecimal centerYpdj;

    @ApiModelProperty("中心药房药品类型")
    private String centerYplx;

    @ApiModelProperty("中心药房药品库存数量")
    private BigDecimal centerKucunsl;

    @ApiModelProperty("中心药房ID")
    private String centerStoreId;

    @ApiModelProperty("中心药房转换系数")
    private String zhuanhuanxs;

    @ApiModelProperty("提纯系数")
    private BigDecimal tcxs;

    @ApiModelProperty("饮片/颗粒 提纯系数计算结果")
    private BigDecimal tcxsMatDose;


    @ApiModelProperty("HIS药品目录ID")
    private String hisYpmlId;

    @ApiModelProperty("HIS药品代码")
    private String hisYpdm;

    @ApiModelProperty("HIS药品名称")
    private String hisYpmc;

    @ApiModelProperty("HIS药品规格ID")
    private String hisGgid;

    @ApiModelProperty("HIS药品规格")
    private String hisYpgg;

    @ApiModelProperty("HIS药品单位")
    private String hisYpdw;

    @ApiModelProperty("HIS药品单价")
    private BigDecimal hisYpdj;

    @ApiModelProperty("HIS药品类型")
    private Integer hisYplx;


    @ApiModelProperty("知识库药品ID")
    private String matId;
    @ApiModelProperty("知识库药品名称")
    private String matName;

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医疗机构ID")
    private String insCode;


    @ApiModelProperty("拼音码")
    private String shuruma1;

    @ApiModelProperty("五笔码")
    private String shuruma2;

    @ApiModelProperty("剂量")
    private BigDecimal dose;

    @ApiModelProperty("用法编码")
    private String usageCode;

    @ApiModelProperty("用法")
    private String usage;

    @ApiModelProperty("是否医保（0自付 1医保）")
    private String isInsurance;

    @ApiModelProperty("日最大剂量（内服）")
    private String dailyMaxDoseIn;
    @ApiModelProperty("日最大剂量（外用）")
    private String dailyMaxDoseExt;
    @ApiModelProperty(value = "医保日最大开药量（制剂）")
    private Double dailyMaxNumPrep;

    private String isMapping;
    private String mapId;
    private String mapType;
    private String matType;
    private String storeId;
    //用作在sql中-不显示在swagger。查询字段-开方页面“医保”列显示数据来源
    @ApiModelProperty(hidden=true)
    private String notPayAlone;

    @ApiModelProperty("显示名称")
    private String showName;

    @ApiModelProperty("是否有其他产地或规格")
    private Integer hasMore;
    @ApiModelProperty("是否有其他产地或规格的提示")
    private String hasMoreTip;

    @ApiModelProperty("剂量限制倍数")
    private String multiple;

    @ApiModelProperty("剂量能否整除（false 不能整除）")
    private Boolean doseIsDivisible;

    @ApiModelProperty("是否包含无库存药品")
    private boolean containNoStock;
    @ApiModelProperty("医保说明")
    private String xzsm;

    /******2021-06-08中药制剂*****/

    @ApiModelProperty(value = "一次用量")
    private BigDecimal matDose;
    @ApiModelProperty(value = "一次剂量单位")
    private String matDoseunit;
    @ApiModelProperty(value = "频次ID")
    private String frequencyId;
    @ApiModelProperty(value = "频次")
    private String frequency;
    @ApiModelProperty(value = "频次系数（次数/天）")
    private String frequencyRate;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "功能主治")
    private String effect;
    @ApiModelProperty(value = "用法用量")
    private String usageDesc;
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;
    @ApiModelProperty(value = "上级药品代码")
    private String parentYpdm;
    // @ApiModelProperty(value = "最大剂量")
    private Double maxdose;

    // @ApiModelProperty(value = "最大剂量")s
    private Double mindose;

    public CenterHisMappingVO(String hisYpmlId, String centerYplx, String centerStoreId) {
        this.hisYpmlId = hisYpmlId;
        this.centerYplx = centerYplx;
        this.centerStoreId = centerStoreId;
    }
}