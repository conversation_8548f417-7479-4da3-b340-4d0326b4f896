package com.jiuzhekan.cbkj.beans.business.record;

import com.jiuzhekan.cbkj.beans.drug.TCenterHisYpmlmx;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "处方明细表")
public class TPrescriptionItem implements Serializable,Cloneable {
    @ApiModelProperty(value = "处方明细UUID")
    private String preItemId;
    @ApiModelProperty(value = "处方UUID")
    private String preId;
    @ApiModelProperty(value = "知识库中药材ID", required = true)
    private String matId;
    @ApiModelProperty(value = "知识库中药材名称", required = true)
    private String matName;
    @ApiModelProperty(value = "药房药品目录代码", required = true)
    private String ypmlCenter;
    @ApiModelProperty(value = "药房药品代码（唯一识别一个药品）", required = true)
    private String ypdmCenter;
    @ApiModelProperty(value = "HIS药品目录代码", required = true)
    private String ypmlHis;
    @ApiModelProperty(value = "HIS药品代码（唯一识别一个药品）", required = true)
    private String ypdmHis;
    @ApiModelProperty(value = "统一药品代码（由开方系统生成）", required = true)
    private String yaopindmTy;
    @ApiModelProperty(value = "HIS药品名称（存HIS的）", required = true)
    private String ypmcHis;
    @ApiModelProperty(value = "HIS药品规格（存HIS的）", required = true)
    private String ypggHis;
    @ApiModelProperty(value = "HIS药品规格代码（存HIS的）", required = true)
    private String ypggdmHis;
    @ApiModelProperty(value = "药房药品名称", required = true)
    private String ypmcCenter;
    @ApiModelProperty(value = "药房药品规格", required = true)
    private String ypggCenter;
    @ApiModelProperty(value = "药房药品规格代码", required = true)
    private String ypggdmCenter;
    @ApiModelProperty(value = "产地ID", required = true)
    private String cdidCenter;
    @ApiModelProperty(value = "产地名称", required = true)
    private String cdmcCenter;
    @ApiModelProperty(value = "一次剂量", required = true)
    private BigDecimal matDose;
    @ApiModelProperty(value = "一次剂量单位ID", required = true)
    private String matDoseunitId;
    @ApiModelProperty(value = "一次剂量单位", required = true)
    private String matDoseunit;
    @ApiModelProperty(value = "天数", required = true)
    private Short matDay;
    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal matNum;
    @ApiModelProperty(value = "包装单位（存药房的）", required = true)
    private String bzdwHis;
    @ApiModelProperty(value = "HIS用法ID", required = true)
    private String yfidHis;
    @ApiModelProperty(value = "HIS用法名称", required = true)
    private String yfmcHis;
    @ApiModelProperty(value = "药房用法ID")
    private String yfidCenter;
    @ApiModelProperty(value = "药房用法名称")
    private String yfmcCenter;
    @ApiModelProperty(value = "频次ID", required = true)
    private String matFrequencyId;
    @ApiModelProperty(value = "频次", required = true)
    private String matFrequency;
    @ApiModelProperty(value = "单价")
    private BigDecimal matXsj;
    @ApiModelProperty(value = "进价")
    private BigDecimal matJhj;
    @ApiModelProperty(value = "序号", required = true)
    private Integer matSeqn;
    @ApiModelProperty(value = "合理用药签名医生ID", required = true)
    private String matDoctor;
    @ApiModelProperty(value = "合理用药签名医生姓名", required = true)
    private String matDoctorname;
    @ApiModelProperty(value = "是否医保（0自付 1医保）", required = true)
    private String isInsurance;
    private Date insertTime;
    @ApiModelProperty(value = "转化系数", required = true)
    private Double zhuanhuanxs;
    @ApiModelProperty(value = "药房ID", required = true)
    private String centerStoreId;
    @ApiModelProperty(value = "备注")
    private String remark;
    private BigDecimal tcxs;
    private BigDecimal tcxsMatDose;

    private TCenterHisYpmlmx mx;

    @ApiModelProperty(value = "预扣批次总价（已乘以帖数）")
    private BigDecimal matTotalPrice;

    @Override
    public TPrescriptionItem clone() {
        TPrescriptionItem item = null;
        try{
            item = (TPrescriptionItem)super.clone();
        }catch(CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return item;
    }


    private  Map<String, List<Map<String,String>>> map ;
}
