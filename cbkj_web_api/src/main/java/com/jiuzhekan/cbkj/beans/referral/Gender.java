package com.jiuzhekan.cbkj.beans.referral;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/24 10:57
 * @Version 1.0
 */
public enum Gender {

    // 定义枚举常量，每个常量对应一个性别代码和性别描述
    UNKNOWN("0", "未知的性别"),
    MALE("1", "男性"),
    FEMALE("2", "女性"),
    UNSPECIFIED("9", "未说明的性别");

    // 定义私有字段来存储性别代码和性别描述
    private final String code;
    private final String description;

    // 枚举构造器，必须是私有的
    Gender(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 提供公共的getter方法来访问性别代码和性别描述
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 如果需要，可以添加一个根据性别代码查找枚举的静态方法
    public static Gender findByCode(String code) {
        for (Gender gender : Gender.values()) {
            if (gender.getCode().equals(code)) {
                return gender;
            }
        }
        throw new IllegalArgumentException("No such gender code: " + code);
    }

    // 其他可能的静态方法或实例方法（根据需要添加）
    public static LinkedHashMap<String, String> getAllEthnicitiesAsMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        for (Gender ethnicity : Gender.values()) {
            map.put(ethnicity.getCode(), ethnicity.getDescription());
        }
        LinkedHashMap<String, String> sortedMap = map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.naturalOrder())) // 根据值排序
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, // 合并函数，这里其实不会用到，因为每个键都是唯一的
                        LinkedHashMap::new // 使用 LinkedHashMap 来保持排序后的顺序
                ));

        return sortedMap;
    }
}
