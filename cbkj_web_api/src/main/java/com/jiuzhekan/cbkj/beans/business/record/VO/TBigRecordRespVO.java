package com.jiuzhekan.cbkj.beans.business.record.VO;

import com.jiuzhekan.cbkj.beans.business.record.TRecordDetail;
import com.jiuzhekan.cbkj.common.utils.PublicVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "大病历响应类")
@Data
public class TBigRecordRespVO implements Serializable {
    @ApiModelProperty(value = "大病历中的单病历集合,按照就诊时间升序排列")
    private List<TRecordRespVO> bigRec;
}