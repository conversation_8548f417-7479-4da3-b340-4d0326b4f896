package com.jiuzhekan.cbkj.service.redis;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RedisService {


    private final RedisTemplate redisTemplate;

    public RedisService(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    public Object get(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                log.debug("Redis缓存获取成功 - key: {}", key);
            } else {
                log.debug("Redis缓存未命中 - key: {}", key);
            }
            return value;
        } catch (Exception e) {
            log.error("Redis缓存获取失败 - key: {}, error: {}", key, e.getMessage());
            return null;
        }
    }

    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.info("Redis缓存设置成功 - key: {}", key);
            return true;
        } catch (Exception e) {
            log.error("Redis缓存设置失败 - key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }

    public boolean set(String key, Object value, long time, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, time, unit);
            log.info("Redis缓存设置成功(带过期时间) - key: {}, time: {}, unit: {}", key, time, unit);
            return true;
        } catch (Exception e) {
            log.error("Redis缓存设置失败(带过期时间) - key: {}, time: {}, unit: {}, error: {}", key, time, unit, e.getMessage());
            return false;
        }
    }

    public long increment(String key) {
        return increment(key, 1);
    }

    public long increment(String key, long delta) {
        try {
            long result = redisTemplate.opsForValue().increment(key, delta);
            log.debug("Redis计数器递增成功 - key: {}, delta: {}, result: {}", key, delta, result);
            return result;
        } catch (Exception e) {
            log.error("Redis计数器递增失败 - key: {}, delta: {}, error: {}", key, delta, e.getMessage());
            return 0;
        }
    }

    public Boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, unit);
            if (Boolean.TRUE.equals(result)) {
                log.debug("Redis设置过期时间成功 - key: {}, timeout: {}, unit: {}", key, timeout, unit);
            } else {
                log.warn("Redis设置过期时间失败 - key: {}, timeout: {}, unit: {}", key, timeout, unit);
            }
            return result;
        } catch (Exception e) {
            log.error("Redis设置过期时间异常 - key: {}, timeout: {}, unit: {}, error: {}", key, timeout, unit, e.getMessage());
            return false;
        }
    }

    /**
     * 清除缓存
     *
     * <AUTHOR>
     * @date 2021/2/4
     */
    public ResEntity clearRedisCache(String value, String key) {
        int deletedCount = 0;

        try {
            if (StringUtils.isNotBlank(value)) {
                Set<String> keys = redisTemplate.keys(value + "*");
                if (keys != null && !keys.isEmpty()) {
                    Long result = redisTemplate.delete(keys);
                    deletedCount += (result != null ? result.intValue() : 0);
                    log.info("Redis批量清除缓存成功 - pattern: {}*, 删除数量: {}", value, result);
                } else {
                    log.info("Redis批量清除缓存 - pattern: {}*, 未找到匹配的key", value);
                }
            }
            if (StringUtils.isNotBlank(key)) {
                Boolean result = redisTemplate.delete(key);
                if (Boolean.TRUE.equals(result)) {
                    deletedCount++;
                    log.info("Redis单个缓存清除成功 - key: {}", key);
                } else {
                    log.warn("Redis单个缓存清除失败 - key: {} (可能不存在)", key);
                }
            }

            log.info("Redis缓存清除操作完成 - 总删除数量: {}", deletedCount);
            return ResEntity.entity(true, "缓存清除成功，删除" + deletedCount + "个key", null);
        } catch (Exception e) {
            log.error("Redis缓存清除失败 - value: {}, key: {}, error: {}", value, key, e.getMessage(), e);
            return ResEntity.entity(false, "缓存清除失败: " + e.getMessage(), null);
        }
    }

    /**
     * 删除单个缓存
     */
    public boolean delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            if (Boolean.TRUE.equals(result)) {
                log.info("Redis缓存删除成功 - key: {}", key);
                return true;
            } else {
                log.warn("Redis缓存删除失败 - key: {} (可能不存在)", key);
                return false;
            }
        } catch (Exception e) {
            log.error("Redis缓存删除异常 - key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }

    /**
     * 检查key是否存在
     */
    public boolean hasKey(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            log.debug("Redis检查key存在性 - key: {}, exists: {}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis检查key存在性异常 - key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }


}